#!/usr/bin/env python3
"""
GitLab Merge Request Monitor - PyQt6 Version
A simplified version focusing on core functionality with better responsiveness.
"""

import sys
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QGridLayout, QLabel, QLineEdit, QPushButton,
                            QComboBox, QTableWidget, QTableWidgetItem, QProgressBar,
                            QCheckBox, QMessageBox, QGroupBox, QHeaderView,
                            QAbstractItemView, QStatusBar, QMenu)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer, QUrl
from PyQt6.QtGui import QFont, QColor, QDesktopServices, QAction
import requests
import json
from datetime import datetime
import threading
import time
from typing import List, Dict, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import webbrowser
import os
from dataclasses import dataclass

# Import the existing classes we need
import sys
sys.path.append('.')
from main_customtkinter_backup import GitLabAPI, MergeRequest, ConfigManager

class ********************(QMainWindow):
    """PyQt6 version of GitLab MR Monitor"""
    
    # Define signals for thread communication
    connection_success = pyqtSignal(str, dict)  # username, user_data
    connection_error = pyqtSignal(str)  # error_message
    projects_loaded = pyqtSignal(list)  # projects list
    mrs_loaded = pyqtSignal(list)  # merge requests list

    def __init__(self):
        super().__init__()
        self.setWindowTitle("GitLab MR Monitor - PyQt6")
        self.setGeometry(100, 100, 1200, 800)
        
        # Initialize components
        self.config_manager = ConfigManager()
        self.gitlab_api = None
        self.merge_requests = []
        self.projects = []
        self.current_user = None
        
        # Connect signals
        self.connection_success.connect(self.on_connection_success)
        self.connection_error.connect(self.on_connection_error)
        self.projects_loaded.connect(self.on_projects_loaded)
        self.mrs_loaded.connect(self.on_mrs_loaded)
        
        # Setup UI
        self.setup_dark_theme()
        self.setup_ui()
        self.load_saved_config()

    def setup_dark_theme(self):
        """Setup dark theme for the application"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2d2d2d;
                color: #ffffff;
            }
            QWidget {
                background-color: #2d2d2d;
                color: #ffffff;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #0d7377;
                border: none;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #14a085;
            }
            QPushButton:pressed {
                background-color: #0a5d61;
            }
            QPushButton:disabled {
                background-color: #555555;
                color: #888888;
            }
            QLineEdit {
                background-color: #404040;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 5px;
                color: #ffffff;
            }
            QComboBox {
                background-color: #404040;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 5px;
                color: #ffffff;
            }
            QTableWidget {
                background-color: #353535;
                alternate-background-color: #404040;
                gridline-color: #555555;
                selection-background-color: #0d7377;
            }
            QHeaderView::section {
                background-color: #404040;
                color: #ffffff;
                padding: 5px;
                border: 1px solid #555555;
                font-weight: bold;
            }
            QCheckBox {
                color: #ffffff;
            }
            QProgressBar {
                border: 1px solid #555555;
                border-radius: 4px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #0d7377;
                border-radius: 3px;
            }
            QStatusBar {
                background-color: #404040;
                color: #ffffff;
            }
        """)

    def setup_ui(self):
        """Setup the main user interface"""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # Setup configuration frame
        self.setup_config_frame(main_layout)

        # Setup control frame
        self.setup_control_frame(main_layout)

        # Setup MR list frame
        self.setup_mr_list_frame(main_layout)

        # Setup status bar
        self.setup_status_bar()

    def setup_config_frame(self, parent_layout):
        """Setup configuration input frame"""
        # Create configuration group box
        config_group = QGroupBox("GitLab Configuration")
        config_layout = QGridLayout(config_group)

        # GitLab URL
        url_label = QLabel("GitLab URL:")
        self.url_entry = QLineEdit()
        self.url_entry.setPlaceholderText("https://gitlab.com or https://gitlab.yourcompany.com")
        self.url_entry.textChanged.connect(self.on_credentials_change)

        config_layout.addWidget(url_label, 0, 0)
        config_layout.addWidget(self.url_entry, 0, 1)

        # Access Token
        token_label = QLabel("Access Token:")
        self.token_entry = QLineEdit()
        self.token_entry.setEchoMode(QLineEdit.EchoMode.Password)
        self.token_entry.setPlaceholderText("Your GitLab access token")
        self.token_entry.textChanged.connect(self.on_credentials_change)

        config_layout.addWidget(token_label, 1, 0)
        config_layout.addWidget(self.token_entry, 1, 1)

        # Connect and Test buttons
        button_layout = QHBoxLayout()

        self.connect_btn = QPushButton("Connect")
        self.connect_btn.clicked.connect(self.connect_to_gitlab)
        self.connect_btn.setMinimumWidth(100)

        self.test_btn = QPushButton("Test")
        self.test_btn.clicked.connect(self.test_connection_only)
        self.test_btn.setEnabled(False)
        self.test_btn.setMinimumWidth(100)

        button_layout.addWidget(self.connect_btn)
        button_layout.addWidget(self.test_btn)
        button_layout.addStretch()

        config_layout.addLayout(button_layout, 0, 2, 2, 1)

        # Connection status
        self.connection_status = QLabel("Not connected")
        self.connection_status.setStyleSheet("color: red;")
        config_layout.addWidget(self.connection_status, 2, 0, 1, 2)

        # SSL verification option
        self.verify_ssl_cb = QCheckBox("Verify SSL certificates (uncheck for self-signed)")
        self.verify_ssl_cb.setChecked(False)  # Default to disabled for compatibility
        config_layout.addWidget(self.verify_ssl_cb, 3, 0, 1, 3)

        parent_layout.addWidget(config_group)

        # Project filtering options
        filter_group = QGroupBox("Filtering Options")
        filter_layout = QGridLayout(filter_group)

        # Only my MRs checkbox
        self.only_my_mrs_cb = QCheckBox("Only show my merge requests")
        self.only_my_mrs_cb.setChecked(True)  # Default to only show user's MRs
        filter_layout.addWidget(self.only_my_mrs_cb, 0, 0, 1, 2)

        # Specific projects (REQUIRED - autodiscovery disabled)
        projects_label = QLabel("⚠️ REQUIRED: Specific Projects (comma-separated IDs or names):")
        filter_layout.addWidget(projects_label, 1, 0, 1, 2)

        self.specific_projects_entry = QLineEdit()
        self.specific_projects_entry.setPlaceholderText("123,456 or my-project,group/another-project")
        filter_layout.addWidget(self.specific_projects_entry, 2, 0, 1, 2)

        # Add helpful hint
        hint_label = QLabel("💡 Examples: '123,456' or 'my-project,group/another-project'")
        hint_label.setStyleSheet("color: gray; font-size: 10px;")
        filter_layout.addWidget(hint_label, 3, 0, 1, 2)

        # Updated help text for disabled autodiscovery
        help_text = QLabel("⚠️ Autodiscovery disabled - You MUST specify project IDs or names above")
        help_text.setStyleSheet("color: red; font-size: 10px;")
        filter_layout.addWidget(help_text, 4, 0, 1, 2)

        parent_layout.addWidget(filter_group)

    def setup_control_frame(self, parent_layout):
        """Setup control buttons frame"""
        control_group = QGroupBox("Controls and Filters")
        control_layout = QHBoxLayout(control_group)

        # Refresh button
        self.refresh_btn = QPushButton("Refresh MRs")
        self.refresh_btn.clicked.connect(self.refresh_merge_requests)
        self.refresh_btn.setEnabled(False)
        self.refresh_btn.setMinimumWidth(120)
        control_layout.addWidget(self.refresh_btn)

        # Auto-refresh toggle
        self.auto_refresh_cb = QCheckBox("Auto-refresh (5 min)")
        self.auto_refresh_cb.toggled.connect(self.toggle_auto_refresh)
        control_layout.addWidget(self.auto_refresh_cb)

        # Add spacing before filters
        control_layout.addSpacing(20)

        # State filter (moved left, with proper width)
        state_label = QLabel("State:")
        control_layout.addWidget(state_label)

        self.state_filter = QComboBox()
        self.state_filter.addItems(['all', 'opened', 'merged', 'closed'])
        self.state_filter.setCurrentText('opened')
        self.state_filter.setMinimumWidth(100)  # Ensure dropdown is wide enough
        self.state_filter.currentTextChanged.connect(self.filter_merge_requests)
        control_layout.addWidget(self.state_filter)

        # Add spacing between filters
        control_layout.addSpacing(15)

        # Project filter (moved left, with proper width)
        project_label = QLabel("Project:")
        control_layout.addWidget(project_label)

        self.project_filter = QComboBox()
        self.project_filter.setMinimumWidth(200)  # Wide enough for project names
        self.project_filter.currentTextChanged.connect(self.filter_merge_requests)
        control_layout.addWidget(self.project_filter)

        # Add stretch at the end to push everything left
        control_layout.addStretch()

        parent_layout.addWidget(control_group)

    def setup_mr_list_frame(self, parent_layout):
        """Setup merge request list frame with table"""
        mr_group = QGroupBox("Merge Requests")
        mr_layout = QVBoxLayout(mr_group)

        # Create table widget
        self.mr_table = QTableWidget()
        self.mr_table.setColumnCount(7)

        # Define column headers
        headers = ['ID', 'Title', 'Project', 'Author', 'State', 'Pipeline', 'Created']
        self.mr_table.setHorizontalHeaderLabels(headers)
        
        # Configure table properties
        self.mr_table.setAlternatingRowColors(True)
        self.mr_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.mr_table.setSortingEnabled(True)
        
        # Set column widths
        header = self.mr_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # ID
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)           # Title
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Project
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # Author
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # State
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # Created
        
        # Connect double-click to open MR in browser
        self.mr_table.doubleClicked.connect(self.open_mr_in_browser)

        # Enable context menu for right-click
        self.mr_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.mr_table.customContextMenuRequested.connect(self.show_context_menu)

        mr_layout.addWidget(self.mr_table)
        parent_layout.addWidget(mr_group)

    def setup_status_bar(self):
        """Setup status bar at the bottom"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")

    def on_credentials_change(self):
        """Handle credential changes to enable/disable connect button"""
        url = self.url_entry.text().strip()
        token = self.token_entry.text().strip()
        self.connect_btn.setEnabled(bool(url and token))

    def load_saved_config(self):
        """Load saved configuration"""
        url = self.config_manager.get('gitlab_url', '')
        token = self.config_manager.get('access_token', '')
        verify_ssl = self.config_manager.get('verify_ssl', False)
        only_my_mrs = self.config_manager.get('only_my_mrs', True)
        specific_projects = self.config_manager.get('specific_projects', '')
        auto_refresh = self.config_manager.get('auto_refresh', False)

        if url:
            self.url_entry.setText(url)
        if token:
            self.token_entry.setText(token)

        self.verify_ssl_cb.setChecked(verify_ssl)
        self.only_my_mrs_cb.setChecked(only_my_mrs)
        self.specific_projects_entry.setText(specific_projects)
        self.auto_refresh_cb.setChecked(auto_refresh)

        # Enable test button if credentials are present
        if url and token:
            self.test_btn.setEnabled(True)
            # Auto-connect if credentials are saved
            print(f"DEBUG: Auto-connecting with specific_projects='{specific_projects}'")
            self.connect_to_gitlab()

    def connect_to_gitlab(self):
        """Connect to GitLab with provided credentials"""
        print("DEBUG: Connect button clicked")
        
        url = self.url_entry.text().strip()
        token = self.token_entry.text().strip()

        if not url or not token:
            QMessageBox.critical(self, "Missing Information",
                               "Please enter both GitLab URL and Access Token.")
            return

        self.status_bar.showMessage("Connecting to GitLab...")
        self.connect_btn.setEnabled(False)
        self.connection_status.setText("Connecting...")
        self.connection_status.setStyleSheet("color: orange;")

        def connect_thread():
            try:
                verify_ssl = self.verify_ssl_cb.isChecked()
                self.gitlab_api = GitLabAPI(url, token, verify_ssl)
                success, message = self.gitlab_api.test_connection()

                if success:
                    # Test if we can actually fetch user data
                    try:
                        user_response = requests.get(f"{url.rstrip('/')}/api/v4/user",
                                                   headers={'Private-Token': token},
                                                   timeout=10,
                                                   verify=verify_ssl)
                        if user_response.status_code == 200:
                            user_data = user_response.json()
                            username = user_data.get('username', 'Unknown')

                            # Save configuration
                            self.config_manager.set('gitlab_url', url)
                            self.config_manager.set('access_token', token)
                            self.config_manager.set('verify_ssl', verify_ssl)
                            self.config_manager.set('only_my_mrs', self.only_my_mrs_cb.isChecked())
                            self.config_manager.set('specific_projects', self.specific_projects_entry.text())
                            self.config_manager.set('auto_refresh', self.auto_refresh_cb.isChecked())

                            # Emit signal to update UI in main thread
                            self.connection_success.emit(username, user_data)
                        else:
                            self.connection_error.emit("Connected but cannot fetch user data. Check token permissions.")
                    except Exception as e:
                        self.connection_error.emit(f"Connected but error fetching user data: {str(e)}")
                else:
                    self.connection_error.emit(message)

            except Exception as e:
                self.connection_error.emit(f"Unexpected connection error: {str(e)}")

        threading.Thread(target=connect_thread, daemon=True).start()

    def on_connection_success(self, username, user_data):
        """Handle successful GitLab connection"""
        # Store current user data for filtering
        self.current_user = user_data

        if username:
            status_text = f"Connected as {username}"
            self.connection_status.setText(status_text)
            self.connection_status.setStyleSheet("color: green;")
            self.status_bar.showMessage(f"Connected to GitLab as {username}")
        else:
            self.connection_status.setText("Connected")
            self.connection_status.setStyleSheet("color: green;")
            self.status_bar.showMessage("Connected to GitLab")

        # Enable buttons
        self.connect_btn.setEnabled(True)
        self.test_btn.setEnabled(True)
        self.refresh_btn.setEnabled(True)

        # Load projects first, then MRs will be loaded automatically
        self.load_projects()

    def on_connection_error(self, error_msg):
        """Handle GitLab connection error"""
        self.connection_status.setText(f"Error: {error_msg[:30]}...")
        self.connection_status.setStyleSheet("color: red;")
        self.connect_btn.setEnabled(True)
        self.status_bar.showMessage("Connection failed")
        QMessageBox.critical(self, "Connection Error", error_msg)

    def load_projects(self):
        """Load user projects for filtering"""
        if not self.gitlab_api:
            return

        self.status_bar.showMessage("Loading projects...")

        def load_thread():
            try:
                print("DEBUG: Loading projects...")

                # Get specific projects from configuration
                specific_projects = self.specific_projects_entry.text().strip()
                if not specific_projects:
                    self.projects_loaded.emit([])
                    return

                # Use the existing project loading logic from the original
                projects = self.gitlab_api._get_specific_projects(
                    specific_projects,
                    lambda progress, text: None  # Progress callback
                )
                print(f"DEBUG: Final result: {len(projects)} projects loaded")

                if projects:
                    self.projects_loaded.emit(projects)
                else:
                    print("DEBUG: No projects found")
                    self.projects_loaded.emit([])

            except Exception as e:
                print(f"DEBUG: Error loading projects: {e}")
                self.projects_loaded.emit([])

        threading.Thread(target=load_thread, daemon=True).start()

    def on_projects_loaded(self, projects):
        """Handle loaded projects"""
        self.projects = projects
        self.update_project_filter(projects)
        if projects:
            self.status_bar.showMessage(f"Loaded {len(projects)} projects")
            # Auto-refresh MRs after projects are loaded
            self.refresh_merge_requests()
        else:
            self.status_bar.showMessage("No projects found")

    def update_project_filter(self, projects):
        """Update project filter dropdown"""
        self.project_filter.clear()
        self.project_filter.addItem('All Projects')
        for project in projects:
            # Use path_with_namespace to match MR project_name format
            project_name = project.get('path_with_namespace', project.get('name', f"Project {project.get('id')}"))
            self.project_filter.addItem(project_name)
            print(f"DEBUG: Added project to filter: '{project_name}'")

    def refresh_merge_requests(self):
        """Refresh merge requests from GitLab"""
        print("DEBUG: Refresh button clicked")

        if not self.gitlab_api:
            print("DEBUG: No GitLab API connection")
            return

        if not hasattr(self, 'projects') or not self.projects:
            print("DEBUG: No projects loaded yet")
            self.status_bar.showMessage("No projects loaded yet...")
            return

        print(f"DEBUG: Refreshing MRs for {len(self.projects)} projects")
        self.status_bar.showMessage("Fetching merge requests...")
        self.refresh_btn.setEnabled(False)

        def refresh_thread():
            try:
                # Get filter settings
                states = ['opened'] if self.state_filter.currentText() == 'opened' else [self.state_filter.currentText()]
                if self.state_filter.currentText() == 'all':
                    states = ['opened', 'merged', 'closed']

                author_id = None
                if self.only_my_mrs_cb.isChecked() and self.current_user:
                    author_id = self.current_user.get('id')

                print(f"DEBUG: Fetching states: {states}")
                if author_id:
                    print(f"DEBUG: Filtering by author ID: {author_id}")

                # Fetch MRs for all projects and states
                all_mrs = []
                total_tasks = len(self.projects) * len(states)
                completed_tasks = 0

                with ThreadPoolExecutor(max_workers=5) as executor:
                    fetch_tasks = []
                    for project in self.projects:
                        for state in states:
                            task = executor.submit(self._fetch_mrs_for_project_state, project, state, author_id)
                            fetch_tasks.append(task)

                    for future in as_completed(fetch_tasks):
                        try:
                            mrs = future.result()
                            if mrs:
                                all_mrs.extend(mrs)
                                print(f"DEBUG: ✓ {len(mrs)} MRs")
                        except Exception as e:
                            print(f"DEBUG: ✗ Error: {e}")

                        completed_tasks += 1

                print(f"DEBUG: Completed {len(fetch_tasks)} tasks - Total MRs: {len(all_mrs)}")
                self.mrs_loaded.emit(all_mrs)

            except Exception as e:
                print(f"DEBUG: Error in refresh_thread: {e}")
                self.mrs_loaded.emit([])

        threading.Thread(target=refresh_thread, daemon=True).start()

    def _fetch_mrs_for_project_state(self, project, state, author_id):
        """Helper method to fetch MRs for a single project and state"""
        return self.gitlab_api.get_merge_requests(project['id'], state=state, author_id=author_id)

    def on_mrs_loaded(self, merge_requests):
        """Handle loaded merge requests"""
        self.merge_requests = merge_requests
        self.update_mr_table()
        self.refresh_btn.setEnabled(True)
        self.status_bar.showMessage(f"Loaded {len(merge_requests)} merge requests")

    def update_mr_table(self):
        """Update the MR table with current data and age-based coloring"""
        self.mr_table.setRowCount(len(self.merge_requests))

        # Calculate age-based colors for all MRs
        age_colors = self.calculate_age_based_colors(self.merge_requests)

        for row, mr in enumerate(self.merge_requests):
            # Set table items
            self.mr_table.setItem(row, 0, QTableWidgetItem(str(mr.id)))
            self.mr_table.setItem(row, 1, QTableWidgetItem(mr.title))
            self.mr_table.setItem(row, 2, QTableWidgetItem(mr.project_name))
            self.mr_table.setItem(row, 3, QTableWidgetItem(mr.author))
            self.mr_table.setItem(row, 4, QTableWidgetItem(mr.state))
            self.mr_table.setItem(row, 5, QTableWidgetItem(mr.pipeline_status or 'N/A'))
            self.mr_table.setItem(row, 6, QTableWidgetItem(self.format_date(mr.created_at)))

            # Apply age-based row coloring
            if row < len(age_colors):
                self.apply_age_based_row_colors(row, age_colors[row])

            # Apply pipeline status coloring to pipeline column
            self.apply_pipeline_status_color(row, mr.pipeline_status)

    def calculate_age_based_colors(self, merge_requests):
        """Calculate age-based colors for MRs (oldest=red, newest=green)"""
        if not merge_requests:
            return []

        # Parse creation dates and calculate ages in days
        now = datetime.now()
        mr_ages = []

        for mr in merge_requests:
            try:
                # Parse the creation date
                if hasattr(mr, 'created_at') and mr.created_at:
                    created_dt = datetime.fromisoformat(mr.created_at.replace('Z', '+00:00'))
                    # Convert to local time for age calculation
                    created_dt = created_dt.replace(tzinfo=None)
                    age_days = (now - created_dt).total_seconds() / 86400  # Convert to days
                else:
                    age_days = 0  # Default for missing dates

                mr_ages.append(age_days)

            except Exception as e:
                print(f"DEBUG: Error parsing date for MR {mr.id}: {e}")
                mr_ages.append(0)  # Default age

        print(f"DEBUG: Calculated ages for {len(mr_ages)} MRs")

        # Find min and max ages
        if not mr_ages:
            return []

        min_age = min(mr_ages)
        max_age = max(mr_ages)
        print(f"DEBUG: Age range: {min_age:.1f} to {max_age:.1f} days")

        # If all MRs have the same age, use a subtle neutral color
        if max_age == min_age:
            print("DEBUG: All MRs have same age, using neutral colors")
            return ['#404040'] * len(merge_requests)  # Subtle gray instead of dark

        # Calculate colors for each MR
        colors = []
        for age in mr_ages:
            # Normalize age to 0-1 range (0 = newest, 1 = oldest)
            if max_age > min_age:
                normalized_age = (age - min_age) / (max_age - min_age)
            else:
                normalized_age = 0

            # Generate color: newest (green) to oldest (red) via yellow
            color = self.interpolate_age_color(normalized_age)
            colors.append(color)

        return colors

    def interpolate_age_color(self, normalized_age):
        """Interpolate color from muted green (newest) to muted red (oldest) via muted yellow"""
        # Clamp to 0-1 range
        normalized_age = max(0, min(1, normalized_age))

        if normalized_age <= 0.5:
            # Muted Green to Muted Yellow (newest to middle-aged)
            t = normalized_age * 2  # Scale to 0-1
            r = int(60 + (140 - 60) * t)    # 60 -> 140 (muted)
            g = int(100 + (140 - 100) * t)  # 100 -> 140 (muted)
            b = int(60 + (50 - 60) * t)     # 60 -> 50 (muted)
        else:
            # Muted Yellow to Muted Red (middle-aged to oldest)
            t = (normalized_age - 0.5) * 2  # Scale to 0-1
            r = int(140 + (130 - 140) * t)  # 140 -> 130 (muted)
            g = int(140 + (70 - 140) * t)   # 140 -> 70 (muted)
            b = int(50 + (60 - 50) * t)     # 50 -> 60 (muted)

        return f"#{r:02x}{g:02x}{b:02x}"

    def apply_age_based_row_colors(self, row, background_color):
        """Apply age-based row styling with calculated background color"""
        # Calculate appropriate text color based on background brightness
        text_color = self.get_contrasting_text_color(background_color)

        # Apply colors to all cells in the row
        for col in range(self.mr_table.columnCount()):
            item = self.mr_table.item(row, col)
            if item:
                item.setBackground(QColor(background_color))
                item.setForeground(QColor(text_color))

    def get_contrasting_text_color(self, background_color):
        """Get contrasting text color (white or black) based on background brightness"""
        # Remove # and convert to RGB
        hex_color = background_color.lstrip('#')
        r = int(hex_color[0:2], 16)
        g = int(hex_color[2:4], 16)
        b = int(hex_color[4:6], 16)

        # Calculate brightness using standard formula
        brightness = (r * 0.299 + g * 0.587 + b * 0.114)

        # Return white for dark backgrounds, black for light backgrounds
        return '#ffffff' if brightness < 128 else '#000000'

    def apply_pipeline_status_color(self, row, pipeline_status):
        """Apply color coding to pipeline status column based on status"""
        # Pipeline status colors (muted versions for better readability)
        pipeline_colors = {
            'success': '#2d5a3d',     # Muted green
            'failed': '#5a2d2d',      # Muted red
            'running': '#2d3d5a',     # Muted blue
            'pending': '#5a5a2d',     # Muted yellow
            'canceled': '#4a4a4a',    # Muted gray
            'skipped': '#4a4a4a',     # Muted gray
            'No Pipeline': '#3a3a3a', # Dark gray
            'N/A': '#3a3a3a'          # Dark gray
        }

        # Get the pipeline status item (column 5)
        pipeline_item = self.mr_table.item(row, 5)
        if pipeline_item:
            status = pipeline_status or 'N/A'
            color = pipeline_colors.get(status, '#3a3a3a')  # Default to dark gray

            # Set background color
            pipeline_item.setBackground(QColor(color))

            # Set contrasting text color
            text_color = self.get_contrasting_text_color(color)
            pipeline_item.setForeground(QColor(text_color))

    def format_date(self, date_str):
        """Format date string for display"""
        try:
            dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            return dt.strftime('%Y-%m-%d %H:%M')
        except:
            return date_str

    def filter_merge_requests(self):
        """Filter merge requests based on state and project"""
        try:
            print("DEBUG: Filtering merge requests...")

            if not hasattr(self, 'merge_requests') or not self.merge_requests:
                print("DEBUG: No merge requests to filter")
                return

            # Get current filter values
            state_filter = self.state_filter.currentText()
            project_filter = self.project_filter.currentText()

            print(f"DEBUG: Applying filters - State: {state_filter}, Project: {project_filter}")

            # Start with all MRs
            filtered_mrs = self.merge_requests.copy()

            # Apply state filter
            if state_filter != 'all':
                filtered_mrs = [mr for mr in filtered_mrs if mr.state == state_filter]
                print(f"DEBUG: After state filter: {len(filtered_mrs)} MRs")

            # Apply project filter
            if project_filter != 'All Projects' and project_filter:
                print(f"DEBUG: Filtering by project: '{project_filter}'")
                print(f"DEBUG: Available MR project names: {[mr.project_name for mr in filtered_mrs[:3]]}")
                filtered_mrs = [mr for mr in filtered_mrs if mr.project_name == project_filter]
                print(f"DEBUG: After project filter: {len(filtered_mrs)} MRs")

            # Store filtered results temporarily
            original_mrs = self.merge_requests
            self.merge_requests = filtered_mrs

            # Update table with filtered results
            self.update_mr_table()

            # Restore original list
            self.merge_requests = original_mrs

            print(f"DEBUG: Displaying {len(filtered_mrs)} filtered MRs")

        except Exception as e:
            print(f"DEBUG: Error in filter_merge_requests: {e}")
            # If filtering fails, just show all MRs
            if hasattr(self, 'merge_requests'):
                self.update_mr_table()

    def test_connection_only(self):
        """Test connection without saving credentials"""
        print("DEBUG: Test button clicked")

        url = self.url_entry.text().strip()
        token = self.token_entry.text().strip()

        if not url or not token:
            QMessageBox.warning(self, "Missing Information",
                               "Please enter both GitLab URL and Access Token to test the connection.")
            return

        self.status_bar.showMessage("Testing connection...")
        self.test_btn.setEnabled(False)
        self.connection_status.setText("Testing...")
        self.connection_status.setStyleSheet("color: orange;")

        def test_thread():
            try:
                verify_ssl = self.verify_ssl_cb.isChecked()
                test_api = GitLabAPI(url, token, verify_ssl)
                success, message = test_api.test_connection()

                if success:
                    self.connection_success.emit("Test User", {})
                else:
                    self.connection_error.emit(message)

            except Exception as e:
                self.connection_error.emit(f"Test failed: {str(e)}")

        threading.Thread(target=test_thread, daemon=True).start()

    def toggle_auto_refresh(self):
        """Toggle auto-refresh functionality"""
        auto_refresh = self.auto_refresh_cb.isChecked()
        self.config_manager.set('auto_refresh', auto_refresh)

        if auto_refresh:
            self.start_auto_refresh()
            self.status_bar.showMessage("Auto-refresh enabled")
        else:
            self.stop_auto_refresh()
            self.status_bar.showMessage("Auto-refresh disabled")

    def start_auto_refresh(self):
        """Start auto-refresh timer"""
        if hasattr(self, 'auto_refresh_timer'):
            self.auto_refresh_timer.stop()

        self.auto_refresh_timer = QTimer()
        self.auto_refresh_timer.timeout.connect(self.refresh_merge_requests)
        self.auto_refresh_timer.start(300000)  # 5 minutes

    def stop_auto_refresh(self):
        """Stop auto-refresh timer"""
        if hasattr(self, 'auto_refresh_timer'):
            self.auto_refresh_timer.stop()

    def show_context_menu(self, position):
        """Show context menu on right-click"""
        # Check if we clicked on a valid row
        item = self.mr_table.itemAt(position)
        if item is None:
            return

        row = item.row()
        if row < 0 or row >= len(self.merge_requests):
            return

        # Create context menu
        context_menu = QMenu(self)

        # Add "Open Link" action
        open_action = QAction("Open Link", self)
        open_action.triggered.connect(lambda: self.open_mr_in_browser_from_row(row))
        context_menu.addAction(open_action)

        # Add separator
        context_menu.addSeparator()

        # Add "Refresh" action
        refresh_action = QAction("Refresh", self)
        refresh_action.triggered.connect(self.refresh_merge_requests)
        context_menu.addAction(refresh_action)

        # Show context menu at cursor position
        context_menu.exec(self.mr_table.mapToGlobal(position))

    def open_mr_in_browser_from_row(self, row):
        """Open MR in browser from specific row"""
        if row >= 0 and row < len(self.merge_requests):
            mr = self.merge_requests[row]
            if hasattr(mr, 'web_url') and mr.web_url:
                print(f"DEBUG: Opening MR {mr.id} in browser: {mr.web_url}")
                QDesktopServices.openUrl(QUrl(mr.web_url))
            else:
                print(f"DEBUG: No web_url found for MR {mr.id}")
        else:
            print(f"DEBUG: Invalid row {row} or no MRs loaded")

    def open_mr_in_browser(self, index=None):
        """Open selected MR in browser"""
        # Handle both double-click (with index) and manual calls (without index)
        if index is not None:
            # Called from doubleClicked signal - index is a QModelIndex
            row = index.row()
        else:
            # Called manually - use current selection
            row = self.mr_table.currentRow()

        self.open_mr_in_browser_from_row(row)


def main():
    """Main entry point"""
    try:
        app = QApplication(sys.argv)
        app.setApplicationName("GitLab MR Monitor")
        app.setStyle('Fusion')
        
        window = ********************()
        window.show()
        
        # Center window on screen
        screen = app.primaryScreen().geometry()
        window_geometry = window.frameGeometry()
        center_point = screen.center()
        window_geometry.moveCenter(center_point)
        window.move(window_geometry.topLeft())
        
        sys.exit(app.exec())
    except Exception as e:
        print(f"Application error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
