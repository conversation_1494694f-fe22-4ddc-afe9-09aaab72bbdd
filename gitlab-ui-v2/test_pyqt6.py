#!/usr/bin/env python3
"""
Simple PyQt6 test to verify installation and basic functionality
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel, QPushButton
from PyQt6.QtCore import Qt

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("PyQt6 Test")
        self.setGeometry(100, 100, 400, 300)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        layout = QVBoxLayout(central_widget)
        
        # Add widgets
        label = QLabel("PyQt6 is working!")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(label)
        
        button = QPushButton("Click me!")
        button.clicked.connect(self.button_clicked)
        layout.addWidget(button)
        
        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)
    
    def button_clicked(self):
        self.status_label.setText("Button clicked!")

def main():
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
