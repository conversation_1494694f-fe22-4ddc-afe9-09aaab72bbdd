#!/usr/bin/env python3
"""
GitLab Merge Request Monitor - PyQt6 Version
A simplified version focusing on core functionality with better responsiveness.
"""

import sys
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QGridLayout, QLabel, QLineEdit, QPushButton, 
                            QComboBox, QTableWidget, QTableWidgetItem, QProgressBar,
                            QCheckBox, QMessageBox, QGroupBox, QHeaderView, 
                            QAbstractItemView, QStatusBar)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QColor, QDesktopServices, QAction
import requests
import json
from datetime import datetime
import threading
import time
from typing import List, Dict, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import webbrowser
import os
from dataclasses import dataclass

# Import the existing classes we need
import sys
sys.path.append('.')
from main_customtkinter_backup import GitLabAPI, MergeRequest, ConfigManager

class ********************(QMainWindow):
    """PyQt6 version of GitLab MR Monitor"""
    
    # Define signals for thread communication
    connection_success = pyqtSignal(str, dict)  # username, user_data
    connection_error = pyqtSignal(str)  # error_message
    projects_loaded = pyqtSignal(list)  # projects list
    mrs_loaded = pyqtSignal(list)  # merge requests list

    def __init__(self):
        super().__init__()
        self.setWindowTitle("GitLab MR Monitor - PyQt6")
        self.setGeometry(100, 100, 1200, 800)
        
        # Initialize components
        self.config_manager = ConfigManager()
        self.gitlab_api = None
        self.merge_requests = []
        self.projects = []
        self.current_user = None
        
        # Connect signals
        self.connection_success.connect(self.on_connection_success)
        self.connection_error.connect(self.on_connection_error)
        self.mrs_loaded.connect(self.on_mrs_loaded)
        
        # Setup UI
        self.setup_dark_theme()
        self.setup_ui()
        self.load_saved_config()

    def setup_dark_theme(self):
        """Setup dark theme for the application"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2d2d2d;
                color: #ffffff;
            }
            QWidget {
                background-color: #2d2d2d;
                color: #ffffff;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #0d7377;
                border: none;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #14a085;
            }
            QPushButton:pressed {
                background-color: #0a5d61;
            }
            QPushButton:disabled {
                background-color: #555555;
                color: #888888;
            }
            QLineEdit {
                background-color: #404040;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 5px;
                color: #ffffff;
            }
            QComboBox {
                background-color: #404040;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 5px;
                color: #ffffff;
            }
            QTableWidget {
                background-color: #353535;
                alternate-background-color: #404040;
                gridline-color: #555555;
                selection-background-color: #0d7377;
            }
            QHeaderView::section {
                background-color: #404040;
                color: #ffffff;
                padding: 5px;
                border: 1px solid #555555;
                font-weight: bold;
            }
            QCheckBox {
                color: #ffffff;
            }
            QProgressBar {
                border: 1px solid #555555;
                border-radius: 4px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #0d7377;
                border-radius: 3px;
            }
            QStatusBar {
                background-color: #404040;
                color: #ffffff;
            }
        """)

    def setup_ui(self):
        """Setup the main user interface"""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # Setup configuration frame
        self.setup_config_frame(main_layout)

        # Setup control frame
        self.setup_control_frame(main_layout)

        # Setup MR list frame
        self.setup_mr_list_frame(main_layout)

        # Setup status bar
        self.setup_status_bar()

    def setup_config_frame(self, parent_layout):
        """Setup configuration input frame"""
        # Create configuration group box
        config_group = QGroupBox("GitLab Configuration")
        config_layout = QGridLayout(config_group)
        
        # GitLab URL
        url_label = QLabel("GitLab URL:")
        self.url_entry = QLineEdit()
        self.url_entry.setPlaceholderText("https://gitlab.com")
        self.url_entry.textChanged.connect(self.on_credentials_change)
        
        config_layout.addWidget(url_label, 0, 0)
        config_layout.addWidget(self.url_entry, 0, 1)

        # Access Token
        token_label = QLabel("Access Token:")
        self.token_entry = QLineEdit()
        self.token_entry.setEchoMode(QLineEdit.EchoMode.Password)
        self.token_entry.setPlaceholderText("Your GitLab access token")
        self.token_entry.textChanged.connect(self.on_credentials_change)
        
        config_layout.addWidget(token_label, 1, 0)
        config_layout.addWidget(self.token_entry, 1, 1)

        # Connect button
        self.connect_btn = QPushButton("Connect")
        self.connect_btn.clicked.connect(self.connect_to_gitlab)
        config_layout.addWidget(self.connect_btn, 0, 2, 2, 1)

        # Connection status
        self.connection_status = QLabel("Not connected")
        self.connection_status.setStyleSheet("color: red;")
        config_layout.addWidget(self.connection_status, 2, 0, 1, 3)

        parent_layout.addWidget(config_group)

    def setup_control_frame(self, parent_layout):
        """Setup control buttons frame"""
        control_group = QGroupBox("Controls")
        control_layout = QHBoxLayout(control_group)

        # Refresh button
        self.refresh_btn = QPushButton("Refresh MRs")
        self.refresh_btn.clicked.connect(self.refresh_merge_requests)
        self.refresh_btn.setEnabled(False)
        control_layout.addWidget(self.refresh_btn)

        # State filter
        control_layout.addWidget(QLabel("State:"))
        self.state_filter = QComboBox()
        self.state_filter.addItems(['all', 'opened', 'merged', 'closed'])
        self.state_filter.setCurrentText('opened')
        self.state_filter.currentTextChanged.connect(self.filter_merge_requests)
        control_layout.addWidget(self.state_filter)

        control_layout.addStretch()
        parent_layout.addWidget(control_group)

    def setup_mr_list_frame(self, parent_layout):
        """Setup merge request list frame with table"""
        mr_group = QGroupBox("Merge Requests")
        mr_layout = QVBoxLayout(mr_group)

        # Create table widget
        self.mr_table = QTableWidget()
        self.mr_table.setColumnCount(6)
        
        # Define column headers
        headers = ['ID', 'Title', 'Project', 'Author', 'State', 'Created']
        self.mr_table.setHorizontalHeaderLabels(headers)
        
        # Configure table properties
        self.mr_table.setAlternatingRowColors(True)
        self.mr_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.mr_table.setSortingEnabled(True)
        
        # Set column widths
        header = self.mr_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # ID
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)           # Title
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Project
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # Author
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # State
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # Created
        
        # Connect double-click to open MR in browser
        self.mr_table.doubleClicked.connect(self.open_mr_in_browser)
        
        mr_layout.addWidget(self.mr_table)
        parent_layout.addWidget(mr_group)

    def setup_status_bar(self):
        """Setup status bar at the bottom"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")

    def on_credentials_change(self):
        """Handle credential changes to enable/disable connect button"""
        url = self.url_entry.text().strip()
        token = self.token_entry.text().strip()
        self.connect_btn.setEnabled(bool(url and token))

    def load_saved_config(self):
        """Load saved configuration"""
        url = self.config_manager.get('gitlab_url', '')
        token = self.config_manager.get('access_token', '')

        if url:
            self.url_entry.setText(url)
        if token:
            self.token_entry.setText(token)

        # Auto-connect if credentials are saved
        if url and token:
            print(f"DEBUG: Auto-connecting...")
            self.connect_to_gitlab()

    def connect_to_gitlab(self):
        """Connect to GitLab with provided credentials"""
        print("DEBUG: Connect button clicked")
        
        url = self.url_entry.text().strip()
        token = self.token_entry.text().strip()

        if not url or not token:
            QMessageBox.critical(self, "Missing Information",
                               "Please enter both GitLab URL and Access Token.")
            return

        self.status_bar.showMessage("Connecting to GitLab...")
        self.connect_btn.setEnabled(False)
        self.connection_status.setText("Connecting...")
        self.connection_status.setStyleSheet("color: orange;")

        def connect_thread():
            try:
                self.gitlab_api = GitLabAPI(url, token, False)  # SSL verification disabled for simplicity
                success, message = self.gitlab_api.test_connection()

                if success:
                    # Save configuration
                    self.config_manager.set('gitlab_url', url)
                    self.config_manager.set('access_token', token)
                    
                    # Emit success signal
                    self.connection_success.emit("User", {})
                else:
                    self.connection_error.emit(message)

            except Exception as e:
                self.connection_error.emit(f"Connection error: {str(e)}")

        threading.Thread(target=connect_thread, daemon=True).start()

    def on_connection_success(self, username, user_data):
        """Handle successful GitLab connection"""
        self.connection_status.setText("Connected")
        self.connection_status.setStyleSheet("color: green;")
        self.status_bar.showMessage("Connected to GitLab")
        self.connect_btn.setEnabled(True)
        self.refresh_btn.setEnabled(True)
        
        # Auto-load MRs
        self.refresh_merge_requests()

    def on_connection_error(self, error_msg):
        """Handle GitLab connection error"""
        self.connection_status.setText(f"Error: {error_msg[:30]}...")
        self.connection_status.setStyleSheet("color: red;")
        self.connect_btn.setEnabled(True)
        self.status_bar.showMessage("Connection failed")
        QMessageBox.critical(self, "Connection Error", error_msg)

    def refresh_merge_requests(self):
        """Refresh merge requests from GitLab"""
        if not self.gitlab_api:
            return

        self.status_bar.showMessage("Fetching merge requests...")
        self.refresh_btn.setEnabled(False)

        def refresh_thread():
            try:
                # For simplicity, just get MRs from a few projects
                # In a real implementation, you'd get user's projects first
                mrs = []
                
                # This is a simplified version - you'd implement proper project discovery
                # For now, just show that the connection works
                self.mrs_loaded.emit(mrs)

            except Exception as e:
                print(f"Error refreshing MRs: {e}")
                self.mrs_loaded.emit([])

        threading.Thread(target=refresh_thread, daemon=True).start()

    def on_mrs_loaded(self, merge_requests):
        """Handle loaded merge requests"""
        self.merge_requests = merge_requests
        self.update_mr_table()
        self.refresh_btn.setEnabled(True)
        self.status_bar.showMessage(f"Loaded {len(merge_requests)} merge requests")

    def update_mr_table(self):
        """Update the MR table with current data"""
        self.mr_table.setRowCount(len(self.merge_requests))
        
        for row, mr in enumerate(self.merge_requests):
            self.mr_table.setItem(row, 0, QTableWidgetItem(str(mr.id)))
            self.mr_table.setItem(row, 1, QTableWidgetItem(mr.title))
            self.mr_table.setItem(row, 2, QTableWidgetItem(mr.project_name))
            self.mr_table.setItem(row, 3, QTableWidgetItem(mr.author))
            self.mr_table.setItem(row, 4, QTableWidgetItem(mr.state))
            self.mr_table.setItem(row, 5, QTableWidgetItem(mr.created_at[:10]))

    def filter_merge_requests(self):
        """Filter merge requests based on state"""
        # Simple filtering implementation
        self.update_mr_table()

    def open_mr_in_browser(self):
        """Open selected MR in browser"""
        current_row = self.mr_table.currentRow()
        if current_row >= 0 and current_row < len(self.merge_requests):
            mr = self.merge_requests[current_row]
            QDesktopServices.openUrl(mr.web_url)


def main():
    """Main entry point"""
    try:
        app = QApplication(sys.argv)
        app.setApplicationName("GitLab MR Monitor")
        app.setStyle('Fusion')
        
        window = ********************()
        window.show()
        
        # Center window on screen
        screen = app.primaryScreen().geometry()
        window_geometry = window.frameGeometry()
        center_point = screen.center()
        window_geometry.moveCenter(center_point)
        window.move(window_geometry.topLeft())
        
        sys.exit(app.exec())
    except Exception as e:
        print(f"Application error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
