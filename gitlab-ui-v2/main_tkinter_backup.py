#!/usr/bin/env python3
"""
GitLab Merge Request Monitor
A neat UI to monitor all your open MRs across projects with color-coded status labels.
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import requests
import json
from datetime import datetime
import threading
import time
from typing import List, Dict, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import webbrowser
import os
from dataclasses import dataclass

@dataclass
class MergeRequest:
    """Data class for merge request information"""
    id: int
    title: str
    state: str
    project_name: str
    project_id: int
    author: str
    created_at: str
    updated_at: str
    web_url: str
    source_branch: str
    target_branch: str
    merge_status: str
    pipeline_status: Optional[str] = None

class GitLabAPI:
    """GitLab API client for fetching merge request data"""

    def __init__(self, base_url: str, token: str, verify_ssl: bool = False):
        self.base_url = base_url.rstrip('/')
        self.token = token
        self.headers = {'Private-Token': token}
        self.verify_ssl = verify_ssl

    def test_connection(self) -> tuple:
        """Test if the GitLab connection is working"""
        try:
            # Validate URL format first
            if not self.base_url.startswith(('http://', 'https://')):
                return False, "Invalid URL format. Please include http:// or https://"

            response = requests.get(
                f"{self.base_url}/api/v4/user",
                headers=self.headers,
                timeout=10,
                verify=self.verify_ssl
            )

            if response.status_code == 200:
                return True, "Connection successful"
            elif response.status_code == 401:
                return False, "Authentication failed. Please check your access token."
            elif response.status_code == 403:
                return False, "Access forbidden. Your token may not have sufficient permissions."
            elif response.status_code == 404:
                return False, "GitLab API not found. Please verify the GitLab URL is correct."
            elif response.status_code == 500:
                return False, "GitLab server error. Please try again later."
            else:
                return False, f"Unexpected response from GitLab (HTTP {response.status_code})"

        except requests.exceptions.ConnectTimeout:
            return False, "Connection timeout. Please check your internet connection and GitLab URL."
        except requests.exceptions.ConnectionError as e:
            if "Name or service not known" in str(e) or "nodename nor servname provided" in str(e):
                return False, "Cannot resolve GitLab hostname. Please check the URL."
            elif "Connection refused" in str(e):
                return False, "Connection refused. GitLab server may be down or URL is incorrect."
            else:
                return False, f"Network connection error: {str(e)}"
        except requests.exceptions.Timeout:
            return False, "Request timeout. GitLab server may be slow or unreachable."
        except requests.exceptions.SSLError:
            return False, "SSL certificate error. The GitLab server's certificate may be invalid."
        except requests.exceptions.RequestException as e:
            return False, f"Request error: {str(e)}"
        except Exception as e:
            return False, f"Unexpected error: {str(e)}"

    def get_user_projects(self, specific_projects_filter=None, progress_callback=None) -> List[Dict]:
        """Get specific projects only - autodiscovery disabled to reduce API calls"""
        print(f"DEBUG: Starting to fetch projects from {self.base_url}")

        # AUTODISCOVERY DISABLED - Only allow specific projects
        if specific_projects_filter and specific_projects_filter.strip():
            print(f"DEBUG: Using ONLY specific projects: {specific_projects_filter}")
            print("DEBUG: Autodiscovery is disabled to reduce API calls")
            return self._get_specific_projects(specific_projects_filter, progress_callback)

        # No autodiscovery - require specific projects to be configured
        print("DEBUG: ❌ AUTODISCOVERY DISABLED - No specific projects configured")
        print("DEBUG: Please configure specific project IDs or names in the 'Specific Projects' field")
        print("DEBUG: Example: '123,456' or 'my-project,another-project'")
        return []

    def _fetch_all_pages_concurrent(self, approach_name, base_params, progress_callback=None):
        """Fetch all pages of projects concurrently"""
        try:
            # First, fetch the first page to get total count and estimate pages
            params = {**base_params, 'page': 1, 'per_page': 100}
            response = requests.get(
                f"{self.base_url}/api/v4/projects",
                headers=self.headers,
                params=params,
                timeout=10,
                verify=self.verify_ssl
            )

            if response.status_code != 200:
                print(f"DEBUG: HTTP {response.status_code} for {approach_name} projects")
                return []

            first_page_projects = response.json()
            if not first_page_projects:
                return []

            # Check headers for pagination info
            total_pages = 1
            if 'X-Total-Pages' in response.headers:
                total_pages = int(response.headers['X-Total-Pages'])
            elif 'X-Total' in response.headers:
                total_items = int(response.headers['X-Total'])
                total_pages = (total_items + 99) // 100  # Round up
            else:
                # Fallback: estimate based on first page
                if len(first_page_projects) == 100:
                    # Likely more pages, but we'll discover them dynamically
                    total_pages = 10  # Conservative estimate

            print(f"DEBUG: Estimated {total_pages} pages for {approach_name} projects")

            all_projects = first_page_projects.copy()

            # Update progress for first page
            if progress_callback:
                progress_callback(100 / total_pages, f"Loading projects... (1/{total_pages} pages)")

            if total_pages <= 1:
                return all_projects

            # Fetch remaining pages concurrently
            completed_pages = 1
            with ThreadPoolExecutor(max_workers=min(6, total_pages - 1)) as executor:
                # Submit tasks for pages 2 through estimated total
                future_to_page = {
                    executor.submit(self._fetch_single_page, base_params, page): page
                    for page in range(2, min(total_pages + 1, 21))  # Cap at 20 pages max
                }

                # Collect results
                for future in as_completed(future_to_page):
                    page_num = future_to_page[future]
                    try:
                        page_projects = future.result()
                        if page_projects:
                            all_projects.extend(page_projects)
                            print(f"DEBUG: ✓ Page {page_num}: {len(page_projects)} projects")
                        else:
                            print(f"DEBUG: ✓ Page {page_num}: empty (end of results)")

                        completed_pages += 1
                        if progress_callback:
                            progress = (completed_pages / total_pages) * 100
                            progress_callback(progress, f"Loading projects... ({completed_pages}/{total_pages} pages)")

                    except Exception as e:
                        print(f"DEBUG: ✗ Page {page_num}: error - {e}")
                        completed_pages += 1
                        if progress_callback:
                            progress = (completed_pages / total_pages) * 100
                            progress_callback(progress, f"Loading projects... ({completed_pages}/{total_pages} pages)")

            return all_projects

        except Exception as e:
            print(f"DEBUG: Error in concurrent page fetching: {e}")
            return []

    def _fetch_single_page(self, base_params, page):
        """Helper method to fetch a single page of projects"""
        try:
            params = {**base_params, 'page': page, 'per_page': 100}
            response = requests.get(
                f"{self.base_url}/api/v4/projects",
                headers=self.headers,
                params=params,
                timeout=10,
                verify=self.verify_ssl
            )

            if response.status_code == 200:
                return response.json()
            else:
                return []
        except Exception as e:
            print(f"DEBUG: Exception fetching page {page}: {e}")
            return []

    def _get_specific_projects(self, projects_filter, progress_callback=None):
        """Get specific projects by ID or name using concurrent fetching"""
        # Parse the filter - could be comma-separated IDs or names
        project_identifiers = [p.strip() for p in projects_filter.split(',') if p.strip()]

        print(f"DEBUG: Fetching {len(project_identifiers)} projects concurrently...")
        start_time = time.time()

        projects = []
        completed_count = 0
        total_count = len(project_identifiers)

        # Use ThreadPoolExecutor for concurrent project fetching
        with ThreadPoolExecutor(max_workers=min(4, len(project_identifiers))) as executor:
            # Submit all project fetch tasks
            future_to_identifier = {
                executor.submit(self._fetch_single_project, identifier): identifier
                for identifier in project_identifiers
            }

            # Collect results as they complete
            for future in as_completed(future_to_identifier):
                identifier = future_to_identifier[future]
                try:
                    project = future.result()
                    if project:
                        projects.append(project)
                        print(f"DEBUG: ✓ {project.get('name', 'Unknown')}")
                    else:
                        print(f"DEBUG: ✗ {identifier} (not found)")
                except Exception as e:
                    print(f"DEBUG: ✗ {identifier} (error: {e})")

                completed_count += 1
                if progress_callback:
                    progress = (completed_count / total_count) * 100
                    progress_callback(progress, f"Loading projects... ({completed_count}/{total_count})")

        elapsed_time = time.time() - start_time
        print(f"DEBUG: Completed project discovery in {elapsed_time:.2f}s - Found {len(projects)} projects")
        return projects

    def _fetch_single_project(self, identifier):
        """Helper method to fetch a single project by ID or name"""
        try:
            # Try as project ID first (if it's numeric)
            if identifier.isdigit():
                project_id = int(identifier)
                response = requests.get(
                    f"{self.base_url}/api/v4/projects/{project_id}",
                    headers=self.headers,
                    timeout=10,
                    verify=self.verify_ssl
                )

                if response.status_code == 200:
                    return response.json()
                else:
                    return None
            else:
                # Try as project name/path
                response = requests.get(
                    f"{self.base_url}/api/v4/projects",
                    headers=self.headers,
                    params={'search': identifier, 'per_page': 100},
                    timeout=10,
                    verify=self.verify_ssl
                )

                if response.status_code == 200:
                    search_results = response.json()
                    # Look for exact matches in name or path
                    for project in search_results:
                        if (project.get('name', '').lower() == identifier.lower() or
                            project.get('path', '').lower() == identifier.lower() or
                            project.get('path_with_namespace', '').lower() == identifier.lower()):
                            return project
                    return None
                else:
                    return None
        except Exception as e:
            print(f"DEBUG: Exception in _fetch_single_project for {identifier}: {e}")
            return None

    def test_api_access(self):
        """Test basic API access and permissions"""
        try:
            print("DEBUG: Testing basic API access...")

            # Test user endpoint
            response = requests.get(
                f"{self.base_url}/api/v4/user",
                headers=self.headers,
                timeout=10,
                verify=self.verify_ssl
            )

            if response.status_code == 200:
                user_data = response.json()
                print(f"DEBUG: User info - ID: {user_data.get('id')}, Username: {user_data.get('username')}")
                print(f"DEBUG: User can_create_project: {user_data.get('can_create_project', 'unknown')}")
            else:
                print(f"DEBUG: User endpoint returned {response.status_code}")

            # Test projects endpoint without filters
            response = requests.get(
                f"{self.base_url}/api/v4/projects",
                headers=self.headers,
                params={'per_page': 1},  # Just get 1 to test access
                timeout=10,
                verify=self.verify_ssl
            )

            print(f"DEBUG: Projects endpoint returned {response.status_code}")
            if response.status_code == 200:
                projects = response.json()
                print(f"DEBUG: Can access projects endpoint, sample size: {len(projects)}")

        except Exception as e:
            print(f"DEBUG: Error testing API access: {e}")

    def get_merge_requests(self, project_id: int, state: str = 'opened', author_id: int = None) -> List[MergeRequest]:
        """Get merge requests for a specific project"""
        try:
            params = {
                'state': state,
                'per_page': 100,
                'with_merge_status_rebase_in_progress': True
            }
            if author_id:
                params['author_id'] = author_id

            response = requests.get(
                f"{self.base_url}/api/v4/projects/{project_id}/merge_requests",
                headers=self.headers,
                params=params,
                timeout=10,
                verify=self.verify_ssl
            )

            if response.status_code != 200:
                print(f"DEBUG: HTTP {response.status_code} for project {project_id}, state {state}")
                return []

            mrs_data = response.json()
            print(f"DEBUG: Raw API returned {len(mrs_data)} MRs for project {project_id}, state {state}")
            merge_requests = []

            for mr_data in mrs_data:
                # Get pipeline status if available
                pipeline_status = self._get_pipeline_status(project_id, mr_data)

                mr = MergeRequest(
                    id=mr_data['iid'],
                    title=mr_data['title'],
                    state=mr_data['state'],
                    project_name=mr_data['references']['full'].split('!')[0],
                    project_id=project_id,
                    author=mr_data['author']['name'],
                    created_at=mr_data['created_at'],
                    updated_at=mr_data['updated_at'],
                    web_url=mr_data['web_url'],
                    source_branch=mr_data['source_branch'],
                    target_branch=mr_data['target_branch'],
                    merge_status=mr_data['merge_status'],
                    pipeline_status=pipeline_status
                )
                merge_requests.append(mr)

            return merge_requests
        except Exception as e:
            print(f"Error fetching MRs for project {project_id}: {e}")
            return []

    def _get_pipeline_status(self, project_id: int, mr_data: dict) -> str:
        """Get pipeline status for a merge request"""
        try:
            # First check if pipeline info is in the MR data
            if mr_data.get('head_pipeline'):
                return mr_data['head_pipeline'].get('status', 'unknown')

            # If not, try to get the latest pipeline for this MR
            mr_iid = mr_data.get('iid')
            if not mr_iid:
                return 'No Pipeline'

            # Make a separate API call to get pipeline info
            pipeline_url = (f"{self.base_url}/api/v4/projects/{project_id}/"
                            f"merge_requests/{mr_iid}/pipelines")
            response = requests.get(
                pipeline_url,
                headers=self.headers,
                params={'per_page': 1, 'order_by': 'id', 'sort': 'desc'},
                timeout=5,
                verify=self.verify_ssl
            )

            if response.status_code == 200:
                pipelines = response.json()
                if pipelines:
                    return pipelines[0].get('status', 'unknown')

            return 'No Pipeline'
        except Exception as e:
            print(f"DEBUG: Pipeline error for MR {mr_data.get('iid')}: {e}")
            return 'No Pipeline'


class ConfigManager:
    """Manage configuration settings"""

    def __init__(self):
        self.config_file = os.path.join(os.path.dirname(__file__), 'config.json')
        self.config = self.load_config()

    def load_config(self) -> Dict:
        """Load configuration from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    return json.load(f)
        except Exception:
            pass
        return {}

    def save_config(self, config: Dict):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
            self.config = config
        except Exception as e:
            print(f"Error saving config: {e}")

    def get(self, key: str, default=None):
        """Get configuration value"""
        return self.config.get(key, default)

    def set(self, key: str, value):
        """Set configuration value"""
        self.config[key] = value
        self.save_config(self.config)


class GitLabMRMonitor:
    """Main application class for GitLab MR monitoring"""

    # Status color mapping
    STATUS_COLORS = {
        'opened': '#28a745',      # Green - New/Open
        'merged': '#6f42c1',      # Purple - Merged
        'closed': '#dc3545',      # Red - Closed
        'draft': '#ffc107',       # Yellow - Draft
        'locked': '#6c757d'       # Gray - Locked
    }

    PIPELINE_COLORS = {
        'success': '#28a745',     # Green
        'failed': '#dc3545',      # Red
        'running': '#007bff',     # Blue
        'pending': '#ffc107',     # Yellow
        'canceled': '#6c757d',    # Gray
        'skipped': '#6c757d'      # Gray
    }

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("GitLab MR Monitor")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f8f9fa')

        # Initialize components
        self.config_manager = ConfigManager()
        self.gitlab_api = None
        self.merge_requests = []
        self.projects = []
        self.auto_refresh = False
        self.refresh_interval = 300  # 5 minutes default
        self.current_user = None  # Store current user info for filtering
        self.configured_tags = set()  # Track configured tags

        # Setup UI
        self.setup_ui()
        self.load_saved_config()

        # Start auto-refresh if enabled
        if self.config_manager.get('auto_refresh', False):
            self.toggle_auto_refresh()

    def setup_ui(self):
        """Setup the main user interface"""
        # Create main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)

        # Setup configuration frame
        self.setup_config_frame(main_frame)

        # Setup control frame
        self.setup_control_frame(main_frame)

        # Setup MR list frame
        self.setup_mr_list_frame(main_frame)

        # Setup status bar
        self.setup_status_bar(main_frame)

    def setup_config_frame(self, parent):
        """Setup configuration input frame"""
        config_frame = ttk.LabelFrame(parent, text="GitLab Configuration", padding="10")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)

        # GitLab URL
        ttk.Label(config_frame, text="GitLab URL:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.url_var = tk.StringVar()
        self._add_trace(self.url_var)
        url_entry = ttk.Entry(config_frame, textvariable=self.url_var, width=50)
        url_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))

        # Access Token
        ttk.Label(config_frame, text="Access Token:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.token_var = tk.StringVar()
        self._add_trace(self.token_var)
        token_entry = ttk.Entry(config_frame, textvariable=self.token_var, show="*", width=50)
        token_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(5, 0))

        # Connect and Test buttons
        button_frame = ttk.Frame(config_frame)
        button_frame.grid(row=0, column=2, rowspan=2, padx=(10, 0))

        self.connect_btn = ttk.Button(button_frame, text="Connect", command=self.connect_to_gitlab)
        self.connect_btn.pack(pady=(0, 5))

        self.test_btn = ttk.Button(button_frame, text="Test", command=self.test_connection_only, state='disabled')
        self.test_btn.pack()

        # Connection status with more detail
        status_frame = ttk.Frame(config_frame)
        status_frame.grid(row=2, column=0, columnspan=3, pady=(5, 0), sticky=(tk.W, tk.E))

        self.connection_status = ttk.Label(status_frame, text="Not connected", foreground="red")
        self.connection_status.pack(side=tk.LEFT)

        # Help button for connection issues
        self.help_btn = ttk.Button(status_frame, text="Connection Help", command=self.show_connection_help)
        self.help_btn.pack(side=tk.RIGHT)

        # SSL verification option
        ssl_frame = ttk.Frame(config_frame)
        ssl_frame.grid(row=3, column=0, columnspan=3, pady=(5, 0), sticky=(tk.W, tk.E))

        self.verify_ssl_var = tk.BooleanVar()
        self.verify_ssl_var.set(False)  # Default to disabled for compatibility
        ssl_cb = ttk.Checkbutton(ssl_frame, text="Verify SSL certificates (uncheck for self-signed certificates)",
                               variable=self.verify_ssl_var)
        ssl_cb.pack(side=tk.LEFT)

        # Project filtering options
        filter_frame = ttk.LabelFrame(config_frame, text="Filtering Options", padding="5")
        filter_frame.grid(row=4, column=0, columnspan=3, pady=(10, 0), sticky=(tk.W, tk.E))
        filter_frame.columnconfigure(1, weight=1)

        # Only my MRs checkbox
        self.only_my_mrs_var = tk.BooleanVar()
        self.only_my_mrs_var.set(True)  # Default to only show user's MRs
        my_mrs_cb = ttk.Checkbutton(filter_frame, text="Only show my merge requests",
                                  variable=self.only_my_mrs_var)
        my_mrs_cb.grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 5))

        # Specific projects (REQUIRED - autodiscovery disabled)
        ttk.Label(filter_frame, text="⚠️ REQUIRED: Specific Projects (comma-separated IDs or names):").grid(
            row=1, column=0, sticky=tk.W, pady=(5, 0))
        self.specific_projects_var = tk.StringVar()
        projects_entry = ttk.Entry(filter_frame, textvariable=self.specific_projects_var, width=60)
        projects_entry.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))

        # Add helpful hint
        hint_label = ttk.Label(filter_frame, text="💡 Examples: '123,456' or 'my-project,group/another-project'",
                              foreground='gray')
        hint_label.grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=(0, 5))

        # Updated help text for disabled autodiscovery
        help_text = ttk.Label(filter_frame,
                            text="⚠️ Autodiscovery disabled - You MUST specify project IDs or names above",
                            font=('TkDefaultFont', 8), foreground='red')
        help_text.grid(row=4, column=0, columnspan=2, sticky=tk.W)

    def setup_control_frame(self, parent):
        """Setup control buttons frame"""
        control_frame = ttk.Frame(parent)
        control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # Refresh button
        self.refresh_btn = ttk.Button(control_frame, text="Refresh MRs", command=self.refresh_merge_requests, state='disabled')
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Auto-refresh toggle
        self.auto_refresh_var = tk.BooleanVar()
        auto_refresh_cb = ttk.Checkbutton(control_frame, text="Auto-refresh (5 min)",
                                        variable=self.auto_refresh_var, command=self.toggle_auto_refresh)
        auto_refresh_cb.pack(side=tk.LEFT, padx=(0, 10))

        # Filter frame
        filter_frame = ttk.LabelFrame(control_frame, text="Filters", padding="5")
        filter_frame.pack(side=tk.LEFT, padx=(10, 0))

        # State filter
        ttk.Label(filter_frame, text="State:").pack(side=tk.LEFT, padx=(0, 5))
        self.state_filter = ttk.Combobox(filter_frame, values=['all', 'opened', 'merged', 'closed'],
                                       state='readonly', width=10)
        self.state_filter.set('opened')
        self.state_filter.pack(side=tk.LEFT, padx=(0, 10))
        self.state_filter.bind('<<ComboboxSelected>>', lambda e: self.filter_merge_requests())

        # Project filter
        ttk.Label(filter_frame, text="Project:").pack(side=tk.LEFT, padx=(0, 5))
        self.project_filter = ttk.Combobox(filter_frame, state='readonly', width=20)
        self.project_filter.pack(side=tk.LEFT)
        self.project_filter.bind('<<ComboboxSelected>>', lambda e: self.filter_merge_requests())

    def setup_mr_list_frame(self, parent):
        """Setup merge request list frame with treeview"""
        list_frame = ttk.LabelFrame(parent, text="Merge Requests", padding="10")
        list_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

        # Create treeview with scrollbars
        tree_frame = ttk.Frame(list_frame)
        tree_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        tree_frame.columnconfigure(0, weight=1)
        tree_frame.rowconfigure(0, weight=1)

        # Define columns
        columns = ('ID', 'Title', 'Project', 'Author', 'State', 'Pipeline', 'Created', 'Updated')
        self.mr_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)

        # Configure larger font for better readability and dark background
        style = ttk.Style()
        style.configure("Treeview",
                        font=('TkDefaultFont', 11),  # Larger font
                        background='#2d2d2d',        # Dark background
                        fieldbackground='#2d2d2d')   # Dark field background
        style.configure("Treeview.Heading",
                        font=('TkDefaultFont', 11, 'bold'),  # Bold headers
                        background='#2d2d2d',                # Dark background
                        fieldbackground='#2d2d2d')           # Dark field bg

        # Configure column headings and widths
        column_widths = {'ID': 60, 'Title': 350, 'Project': 180, 'Author': 140,
                        'State': 90, 'Pipeline': 90, 'Created': 110, 'Updated': 110}

        for col in columns:
            self.mr_tree.heading(col, text=col, command=lambda c=col: self.sort_treeview(c))
            self.mr_tree.column(col, width=column_widths.get(col, 100), minwidth=50)

        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.mr_tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.mr_tree.xview)
        self.mr_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Grid layout
        self.mr_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # Bind double-click to open MR in browser
        self.mr_tree.bind('<Double-1>', self.open_mr_in_browser)

        # Context menu
        self.setup_context_menu()

    def setup_context_menu(self):
        """Setup right-click context menu for MR list"""
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="Open in Browser", command=self.open_mr_in_browser)
        self.context_menu.add_command(label="Copy URL", command=self.copy_mr_url)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Refresh", command=self.refresh_merge_requests)

        self.mr_tree.bind('<Button-2>', self.show_context_menu)  # Right-click on macOS
        self.mr_tree.bind('<Button-3>', self.show_context_menu)  # Right-click on Windows/Linux

    def setup_status_bar(self, parent):
        """Setup status bar at the bottom"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))

        # Left side - status text
        left_frame = ttk.Frame(status_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_label = ttk.Label(left_frame, textvariable=self.status_var)
        status_label.pack(side=tk.LEFT)

        # Progress bar (initially hidden)
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            left_frame,
            variable=self.progress_var,
            maximum=100,
            length=200,
            mode='determinate'
        )
        # Don't pack initially - will be shown when needed

        # Right side - MR count label
        self.mr_count_var = tk.StringVar()
        self.mr_count_var.set("0 MRs")
        count_label = ttk.Label(status_frame, textvariable=self.mr_count_var)
        count_label.pack(side=tk.RIGHT)

    def show_progress_bar(self):
        """Show the progress bar"""
        self.progress_bar.pack(side=tk.LEFT, padx=(10, 0))
        self.progress_var.set(0)

    def hide_progress_bar(self):
        """Hide the progress bar"""
        self.progress_bar.pack_forget()

    def update_progress(self, value, text=None):
        """Update progress bar value and optionally status text"""
        self.progress_var.set(value)
        if text:
            self.status_var.set(text)
        self.root.update_idletasks()

    def load_saved_config(self):
        """Load saved configuration"""
        url = self.config_manager.get('gitlab_url', '')
        token = self.config_manager.get('access_token', '')
        auto_refresh = self.config_manager.get('auto_refresh', False)
        verify_ssl = self.config_manager.get('verify_ssl', False)
        only_my_mrs = self.config_manager.get('only_my_mrs', True)
        specific_projects = self.config_manager.get('specific_projects', '')

        self.url_var.set(url)
        self.token_var.set(token)
        self.auto_refresh_var.set(auto_refresh)
        self.verify_ssl_var.set(verify_ssl)
        self.only_my_mrs_var.set(only_my_mrs)
        self.specific_projects_var.set(specific_projects)

        # Enable test button if credentials are present
        if url and token:
            self.test_btn.configure(state='normal')
            # Auto-connect if credentials are saved
            print(f"DEBUG: Auto-connecting with specific_projects='{self.specific_projects_var.get()}'")
            self.connect_to_gitlab()

    def connect_to_gitlab(self):
        """Connect to GitLab with provided credentials"""
        url = self.url_var.get().strip()
        token = self.token_var.get().strip()

        # Enhanced input validation
        if not url:
            messagebox.showerror("Missing Information",
                               "Please enter your GitLab URL\n\n" +
                               "Examples:\n" +
                               "• https://gitlab.com\n" +
                               "• https://gitlab.yourcompany.com")
            return

        if not token:
            messagebox.showerror("Missing Information",
                               "Please enter your GitLab Access Token\n\n" +
                               "To create a token:\n" +
                               "1. Go to GitLab → User Settings → Access Tokens\n" +
                               "2. Create token with 'read_api' and 'read_repository' scopes\n" +
                               "3. Copy the generated token here")
            return

        # Basic URL validation
        if not url.startswith(('http://', 'https://')):
            messagebox.showerror("Invalid URL",
                               "GitLab URL must start with http:// or https://\n\n" +
                               f"Current URL: {url}\n" +
                               f"Try: https://{url}")
            return

        self.status_var.set("Connecting to GitLab...")
        self.connect_btn.configure(state='disabled')
        self.connection_status.configure(text="Connecting...", foreground="orange")

        def connect_thread():
            try:
                verify_ssl = self.verify_ssl_var.get()
                self.gitlab_api = GitLabAPI(url, token, verify_ssl)
                success, message = self.gitlab_api.test_connection()

                if success:
                    # Test if we can actually fetch user data
                    try:
                        user_response = requests.get(f"{url.rstrip('/')}/api/v4/user",
                                                   headers={'Private-Token': token},
                                                   timeout=10,
                                                   verify=verify_ssl)
                        if user_response.status_code == 200:
                            user_data = user_response.json()
                            username = user_data.get('username', 'Unknown')

                            # Save configuration
                            self.config_manager.set('gitlab_url', url)
                            self.config_manager.set('access_token', token)
                            self.config_manager.set('verify_ssl', verify_ssl)
                            self.config_manager.set('only_my_mrs', self.only_my_mrs_var.get())
                            self.config_manager.set('specific_projects', self.specific_projects_var.get())

                            # Update UI in main thread and pass user data
                            self.root.after(0, self.on_connection_success, username, user_data)
                        else:
                            self.root.after(0, self.on_connection_error,
                                          "Connected but cannot fetch user data. Check token permissions.")
                    except Exception as e:
                        self.root.after(0, self.on_connection_error,
                                      f"Connected but error fetching user data: {str(e)}")
                else:
                    self.root.after(0, self.on_connection_error, message)

            except Exception as e:
                self.root.after(0, self.on_connection_error,
                              f"Unexpected connection error: {str(e)}")

        threading.Thread(target=connect_thread, daemon=True).start()

    def on_connection_success(self, username=None, user_data=None):
        """Handle successful GitLab connection"""
        # Store current user data for filtering
        self.current_user = user_data

        if username:
            status_text = f"Connected as {username}"
            self.connection_status.configure(text=status_text, foreground="green")
            self.status_var.set(f"Connected to GitLab as {username}")
        else:
            self.connection_status.configure(text="Connected", foreground="green")
            self.status_var.set("Connected to GitLab")

        self.connect_btn.configure(state='normal')
        self.test_btn.configure(state='normal')
        self.refresh_btn.configure(state='normal')

        # Load projects first, then MRs will be loaded automatically
        self.load_projects()

    def on_connection_error(self, error_msg):
        """Handle GitLab connection error with detailed feedback"""
        # Truncate very long error messages for the status label
        short_msg = error_msg[:50] + "..." if len(error_msg) > 50 else error_msg
        self.connection_status.configure(text=f"Error: {short_msg}", foreground="red")
        self.connect_btn.configure(state='normal')
        self.status_var.set("Connection failed")

        # Provide detailed error message with troubleshooting tips
        detailed_msg = f"Failed to connect to GitLab:\n\n{error_msg}\n\n"

        # Add specific troubleshooting tips based on error type
        if "Authentication failed" in error_msg:
            detailed_msg += "Troubleshooting:\n"
            detailed_msg += "• Verify your access token is correct\n"
            detailed_msg += "• Check if the token has expired\n"
            detailed_msg += "• Ensure token has 'read_api' and 'read_repository' scopes"
        elif "Cannot resolve" in error_msg or "Name or service not known" in error_msg:
            detailed_msg += "Troubleshooting:\n"
            detailed_msg += "• Check your internet connection\n"
            detailed_msg += "• Verify the GitLab URL is correct\n"
            detailed_msg += "• Try accessing the URL in your browser first"
        elif "Connection refused" in error_msg:
            detailed_msg += "Troubleshooting:\n"
            detailed_msg += "• GitLab server may be down\n"
            detailed_msg += "• Check if you need VPN access\n"
            detailed_msg += "• Verify the URL and port are correct"
        elif "timeout" in error_msg.lower():
            detailed_msg += "Troubleshooting:\n"
            detailed_msg += "• Check your internet connection\n"
            detailed_msg += "• GitLab server may be slow\n"
            detailed_msg += "• Try again in a few moments"
        elif "SSL certificate" in error_msg:
            detailed_msg += "Troubleshooting:\n"
            detailed_msg += "• GitLab server has SSL certificate issues\n"
            detailed_msg += "• Contact your GitLab administrator\n"
            detailed_msg += "• For self-hosted GitLab, check certificate configuration"
        elif "Access forbidden" in error_msg:
            detailed_msg += "Troubleshooting:\n"
            detailed_msg += "• Your token may not have sufficient permissions\n"
            detailed_msg += "• Recreate token with 'read_api' and 'read_repository' scopes\n"
            detailed_msg += "• Check if your account has access to the GitLab instance"
        else:
            detailed_msg += "Troubleshooting:\n"
            detailed_msg += "• Check your GitLab URL and access token\n"
            detailed_msg += "• Verify your internet connection\n"
            detailed_msg += "• Try accessing GitLab in your browser"

        messagebox.showerror("Connection Error", detailed_msg)

    def load_projects(self):
        """Load user projects for filtering"""
        if not self.gitlab_api:
            return

        self.status_var.set("Loading projects...")
        self.show_progress_bar()

        def progress_callback(progress, text):
            """Update progress from background thread"""
            self.root.after(0, self.update_progress, progress, text)

        def load_thread():
            try:
                print("DEBUG: Loading projects...")

                # Get specific projects filter if configured
                specific_projects = self.specific_projects_var.get().strip()

                # Only test general API access if we're doing general project discovery
                if not specific_projects:
                    print("DEBUG: Testing general API access...")
                    self.gitlab_api.test_api_access()
                else:
                    print("DEBUG: Skipping general API test - using specific projects only")

                if specific_projects:
                    print(f"DEBUG: Using specific projects list: {specific_projects}")
                    print("DEBUG: Will NOT scan for all accessible projects")
                else:
                    print("DEBUG: No specific projects configured")
                    print("DEBUG: Will scan for all accessible projects")

                # Get projects based on configuration with progress tracking
                projects = self.gitlab_api.get_user_projects(
                    specific_projects if specific_projects else None,
                    progress_callback
                )
                print(f"DEBUG: Final result: {len(projects)} projects loaded")

                if projects:
                    self.root.after(0, self.update_project_filter, projects)
                    self.root.after(0, lambda: self.update_progress(100, f"Loaded {len(projects)} projects"))
                    self.root.after(0, self.hide_progress_bar)
                else:
                    print("DEBUG: No projects found")
                    self.root.after(0, self.hide_progress_bar)

                    # Check if this is because autodiscovery is disabled
                    specific_projects = self.specific_projects_var.get().strip()
                    if not specific_projects:
                        self.root.after(0, self.on_autodiscovery_disabled_error)
                    else:
                        self.root.after(0, self.on_projects_load_error,
                                      "No projects found with the specified project IDs/names.")
            except Exception as e:
                print(f"DEBUG: Error loading projects: {e}")
                import traceback
                traceback.print_exc()
                self.root.after(0, self.hide_progress_bar)
                self.root.after(0, self.on_projects_load_error, str(e))

        threading.Thread(target=load_thread, daemon=True).start()

    def on_projects_load_error(self, error_msg):
        """Handle project loading errors"""
        self.status_var.set("Failed to load projects")

        detailed_msg = f"Failed to load projects:\n\n{error_msg}\n\n"
        detailed_msg += "Possible causes:\n"
        detailed_msg += "• Your access token may not have sufficient permissions\n"
        detailed_msg += "• You may not be a member of any GitLab projects\n"
        detailed_msg += "• Token needs 'read_api' and 'read_repository' scopes\n"
        detailed_msg += "• Network connectivity issues\n\n"
        detailed_msg += "Solutions:\n"
        detailed_msg += "• Try creating a new access token with full permissions\n"
        detailed_msg += "• Ask your GitLab admin to add you to projects\n"
        detailed_msg += "• Run the test_projects.py script for detailed debugging\n\n"
        detailed_msg += "You can still manually refresh to try again."

        messagebox.showwarning("Project Loading Warning", detailed_msg)

        # Set up a minimal project list so the app doesn't break
        self.projects = []
        self.project_filter['values'] = ['All Projects']
        self.project_filter.set('All Projects')

    def on_autodiscovery_disabled_error(self):
        """Handle the case where autodiscovery is disabled and no specific projects are configured"""
        self.status_var.set("Autodiscovery disabled - Configure specific projects")

        detailed_msg = "🚫 **Autodiscovery is disabled** to reduce API calls.\n\n"
        detailed_msg += "**To use this app, you must configure specific projects:**\n\n"
        detailed_msg += "1. **Find your project IDs or names:**\n"
        detailed_msg += "   • Go to your GitLab project → Settings → General\n"
        detailed_msg += "   • Copy the Project ID (number) or project name\n\n"
        detailed_msg += "2. **Enter them in the 'Specific Projects' field:**\n"
        detailed_msg += "   • Project IDs: `123,456,789`\n"
        detailed_msg += "   • Project names: `my-project,another-project`\n"
        detailed_msg += "   • Full paths: `group/project1,group/project2`\n\n"
        detailed_msg += "3. **Click 'Connect to GitLab' again**\n\n"
        detailed_msg += "**Why autodiscovery is disabled:**\n"
        detailed_msg += "• Reduces API calls from thousands to just a few\n"
        detailed_msg += "• Faster loading and better performance\n"
        detailed_msg += "• Avoids rate limiting on large GitLab instances"

        messagebox.showinfo("Configure Specific Projects", detailed_msg)

        # Set up a minimal project list so the app doesn't break
        self.projects = []
        self.project_filter['values'] = ['All Projects']
        self.project_filter.set('All Projects')

    def update_project_filter(self, projects):
        """Update project filter combobox"""
        print(f"DEBUG: Updating project filter with {len(projects)} projects")
        self.projects = projects
        # Use path_with_namespace for full project path to match MR project_name
        project_names = ['All Projects'] + [p.get('path_with_namespace', p['name']) for p in projects]

        self.project_filter['values'] = project_names
        self.project_filter.set('All Projects')

        # Now that projects are loaded, refresh merge requests
        print("DEBUG: Projects loaded, now refreshing merge requests...")
        self.refresh_merge_requests()

    def refresh_merge_requests(self):
        """Refresh merge requests from GitLab"""
        if not self.gitlab_api:
            print("DEBUG: No GitLab API connection")
            return

        if not hasattr(self, 'projects') or not self.projects:
            print("DEBUG: No projects loaded yet")
            self.status_var.set("No projects loaded yet...")
            return

        print(f"DEBUG: Refreshing MRs for {len(self.projects)} projects")
        self.status_var.set("Fetching merge requests...")
        self.show_progress_bar()
        self.refresh_btn.configure(state='disabled')

        def refresh_thread():
            try:
                all_mrs = []
                state_filter = self.state_filter.get()
                states_to_fetch = ['opened', 'merged', 'closed'] if state_filter == 'all' else [state_filter]

                print(f"DEBUG: Fetching states: {states_to_fetch}")

                # Check if we should filter by author
                author_id = None
                if self.only_my_mrs_var.get() and self.current_user:
                    author_id = self.current_user.get('id')
                    print(f"DEBUG: Filtering by author ID: {author_id}")

                # Create list of tasks for concurrent execution
                fetch_tasks = []
                for project in self.projects:
                    for state in states_to_fetch:
                        fetch_tasks.append((project, state, author_id))

                print(f"DEBUG: Starting {len(fetch_tasks)} concurrent MR fetch tasks")
                start_time = time.time()
                completed_tasks = 0
                total_tasks = len(fetch_tasks)

                # Use ThreadPoolExecutor for concurrent fetching
                with ThreadPoolExecutor(max_workers=min(8, len(fetch_tasks))) as executor:
                    # Submit all tasks
                    future_to_task = {
                        executor.submit(self._fetch_mrs_for_project_state, project, state, author_id): (project, state)
                        for project, state, author_id in fetch_tasks
                    }

                    # Collect results as they complete
                    for future in as_completed(future_to_task):
                        project, state = future_to_task[future]
                        try:
                            mrs = future.result()
                            all_mrs.extend(mrs)
                            print(f"DEBUG: ✓ {project.get('name', 'Unknown')} ({state}): {len(mrs)} MRs")
                        except Exception as e:
                            print(f"DEBUG: ✗ Error fetching {project.get('name', 'Unknown')} ({state}): {e}")

                        completed_tasks += 1
                        progress = (completed_tasks / total_tasks) * 100
                        self.root.after(0, self.update_progress, progress,
                                      f"Fetching MRs... ({completed_tasks}/{total_tasks})")

                elapsed_time = time.time() - start_time
                print(f"DEBUG: Completed {len(fetch_tasks)} tasks in {elapsed_time:.2f}s - Total MRs: {len(all_mrs)}")
                self.root.after(0, self.update_mr_list, all_mrs)
                self.root.after(0, self.hide_progress_bar)

            except Exception as e:
                print(f"DEBUG: Error in refresh_thread: {e}")
                self.root.after(0, self.hide_progress_bar)
                self.root.after(0, self.on_refresh_error, str(e))

        threading.Thread(target=refresh_thread, daemon=True).start()

    def _fetch_mrs_for_project_state(self, project, state, author_id):
        """Helper method to fetch MRs for a single project and state (used by ThreadPoolExecutor)"""
        try:
            return self.gitlab_api.get_merge_requests(project['id'], state, author_id)
        except Exception as e:
            print(f"DEBUG: Error fetching MRs for {project.get('name', 'Unknown')} ({state}): {e}")
            return []

    def update_mr_list(self, merge_requests):
        """Update the MR list in the UI"""
        print(f"DEBUG: Updating MR list with {len(merge_requests)} merge requests")
        self.merge_requests = merge_requests
        self.filter_merge_requests()
        self.refresh_btn.configure(state='normal')
        self.status_var.set(f"Last updated: {datetime.now().strftime('%H:%M:%S')}")

    def on_refresh_error(self, error_msg):
        """Handle refresh error with detailed feedback"""
        self.refresh_btn.configure(state='normal')
        self.status_var.set("Refresh failed")

        detailed_msg = f"Failed to refresh merge requests:\n\n{error_msg}\n\n"

        # Add specific troubleshooting based on error type
        if "timeout" in error_msg.lower():
            detailed_msg += "Troubleshooting:\n"
            detailed_msg += "• GitLab server may be slow or overloaded\n"
            detailed_msg += "• Check your internet connection\n"
            detailed_msg += "• Try again in a few moments"
        elif "401" in error_msg or "Authentication" in error_msg:
            detailed_msg += "Troubleshooting:\n"
            detailed_msg += "• Your access token may have expired\n"
            detailed_msg += "• Reconnect with a fresh token\n"
            detailed_msg += "• Check token permissions"
        elif "403" in error_msg or "forbidden" in error_msg.lower():
            detailed_msg += "Troubleshooting:\n"
            detailed_msg += "• You may have lost access to some projects\n"
            detailed_msg += "• Check your project permissions\n"
            detailed_msg += "• Contact your GitLab administrator"
        else:
            detailed_msg += "Troubleshooting:\n"
            detailed_msg += "• Check your internet connection\n"
            detailed_msg += "• Verify GitLab server is accessible\n"
            detailed_msg += "• Try reconnecting to GitLab"

        messagebox.showerror("Refresh Error", detailed_msg)

    def test_connection_only(self):
        """Test connection without saving credentials"""
        url = self.url_var.get().strip()
        token = self.token_var.get().strip()

        if not url or not token:
            messagebox.showwarning("Missing Information",
                                 "Please enter both GitLab URL and Access Token to test the connection.")
            return

        self.status_var.set("Testing connection...")
        self.test_btn.configure(state='disabled')
        self.connection_status.configure(text="Testing...", foreground="orange")

        def test_thread():
            try:
                verify_ssl = self.verify_ssl_var.get()
                test_api = GitLabAPI(url, token, verify_ssl)
                success, message = test_api.test_connection()

                if success:
                    self.root.after(0, self.on_test_success, message)
                else:
                    self.root.after(0, self.on_test_error, message)

            except Exception as e:
                self.root.after(0, self.on_test_error, f"Test failed: {str(e)}")

        threading.Thread(target=test_thread, daemon=True).start()

    def on_test_success(self, message):
        """Handle successful connection test"""
        self.connection_status.configure(text="Test successful ✓", foreground="green")
        self.test_btn.configure(state='normal')
        self.status_var.set("Connection test successful")
        messagebox.showinfo("Connection Test", f"✓ {message}\n\nYou can now click 'Connect' to save these credentials and start using the application.")

    def on_test_error(self, error_msg):
        """Handle failed connection test"""
        self.connection_status.configure(text="Test failed ✗", foreground="red")
        self.test_btn.configure(state='normal')
        self.status_var.set("Connection test failed")

        # Use the same detailed error handling as connection errors
        self.on_connection_error(error_msg)

    def show_connection_help(self):
        """Show detailed connection help"""
        help_text = """GitLab Connection Help

🔗 GitLab URL:
• Use your GitLab instance URL (e.g., https://gitlab.com)
• For self-hosted: https://gitlab.yourcompany.com
• Must include https:// or http://

🔑 Access Token:
1. Go to GitLab → User Settings → Access Tokens
2. Create a new token with these scopes:
   • read_api (required)
   • read_repository (required)
3. Copy the generated token (you won't see it again!)

🔧 Common Issues:

Authentication Failed (401):
• Token is incorrect or expired
• Recreate your access token

Access Forbidden (403):
• Token doesn't have required permissions
• Add 'read_api' and 'read_repository' scopes

Cannot Resolve Hostname:
• Check your internet connection
• Verify the GitLab URL is correct
• Try accessing the URL in your browser

Connection Timeout:
• GitLab server may be slow
• Check your internet connection
• Try again in a few moments

SSL Certificate Error:
• GitLab server has certificate issues
• For self-signed certificates, uncheck "Verify SSL certificates"
• Contact your GitLab administrator

⚠️  SSL Verification:
• Uncheck "Verify SSL certificates" for self-hosted GitLab with self-signed certificates
• Keep checked for public GitLab instances for security
• Only disable for trusted internal servers

🆘 Still having issues?
• Use the 'Test' button to check your connection
• Verify you can access GitLab in your browser
• Contact your GitLab administrator for help"""

        # Create a new window for help
        help_window = tk.Toplevel(self.root)
        help_window.title("GitLab Connection Help")
        help_window.geometry("600x700")
        help_window.configure(bg='#f8f9fa')

        # Add scrollable text
        text_frame = ttk.Frame(help_window, padding="20")
        text_frame.pack(fill=tk.BOTH, expand=True)

        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=('TkDefaultFont', 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        text_widget.insert(tk.END, help_text)
        text_widget.configure(state='disabled')  # Make read-only

    def _add_trace(self, var):
        """Add trace to variable with compatibility for different Python versions"""
        try:
            # Try new method first (Python 3.6+)
            var.trace_add('write', self.on_credentials_change)
        except AttributeError:
            # Fallback to old method
            var.trace('w', self.on_credentials_change)

    def on_credentials_change(self, *args):
        """Enable/disable test button based on credential input"""
        url = self.url_var.get().strip()
        token = self.token_var.get().strip()

        if url and token:
            self.test_btn.configure(state='normal')
        else:
            self.test_btn.configure(state='disabled')

    def filter_merge_requests(self):
        """Filter and display merge requests based on current filters"""
        print(f"DEBUG: Filtering {len(self.merge_requests)} merge requests")

        # Clear existing items
        for item in self.mr_tree.get_children():
            self.mr_tree.delete(item)

        # Apply filters
        state_filter = self.state_filter.get()
        project_filter = self.project_filter.get()
        print(f"DEBUG: Filters - State: {state_filter}, Project: {project_filter}")

        filtered_mrs = []
        for mr in self.merge_requests:
            # State filter
            if state_filter != 'all' and mr.state != state_filter:
                continue

            # Project filter
            if project_filter != 'All Projects' and mr.project_name != project_filter:
                continue

            filtered_mrs.append(mr)

        # Sort by updated date (newest first)
        filtered_mrs.sort(key=lambda x: x.updated_at, reverse=True)

        # Calculate age-based colors for the filtered MRs
        age_colors = self.calculate_age_based_colors(filtered_mrs)
        print(f"DEBUG: Calculated {len(age_colors)} age-based colors for MRs")

        # Populate tree
        for i, mr in enumerate(filtered_mrs):
            # Format dates
            created = self.format_date(mr.created_at)
            updated = self.format_date(mr.updated_at)

            # Insert item
            item_id = self.mr_tree.insert('', 'end', values=(
                mr.id, mr.title, mr.project_name, mr.author,
                mr.state, mr.pipeline_status or 'N/A', created, updated
            ))

            # Apply age-based row styling
            self.apply_age_based_row_colors(item_id, mr, age_colors[i])

        # Update count
        print(f"DEBUG: Displaying {len(filtered_mrs)} filtered MRs")
        self.mr_count_var.set(f"{len(filtered_mrs)} MRs")

    def calculate_age_based_colors(self, merge_requests):
        """Calculate age-based colors for MRs (oldest=red, newest=green)"""
        if not merge_requests:
            return []

        # Parse creation dates and calculate ages in days
        now = datetime.now()
        mr_ages = []

        for mr in merge_requests:
            try:
                created_dt = datetime.fromisoformat(mr.created_at.replace('Z', '+00:00'))
                # Convert to local timezone for age calculation
                if created_dt.tzinfo:
                    created_dt = created_dt.replace(tzinfo=None)
                age_days = (now - created_dt).total_seconds() / (24 * 3600)
                mr_ages.append(age_days)
            except Exception as e:
                print(f"DEBUG: Error parsing date {mr.created_at}: {e}")
                mr_ages.append(0)  # Default to 0 days if parsing fails

        if not mr_ages:
            return ['#2d2d2d'] * len(merge_requests)

        # Find min and max ages
        min_age = min(mr_ages)
        max_age = max(mr_ages)
        print(f"DEBUG: Age range: {min_age:.1f} to {max_age:.1f} days")

        # If all MRs have the same age, use a neutral color
        if max_age == min_age:
            print("DEBUG: All MRs have same age, using neutral colors")
            return ['#2d2d2d'] * len(merge_requests)

        # Calculate colors for each MR
        colors = []
        for age in mr_ages:
            # Normalize age to 0-1 range (0 = newest, 1 = oldest)
            if max_age > min_age:
                normalized_age = (age - min_age) / (max_age - min_age)
            else:
                normalized_age = 0

            # Generate color: newest (green) to oldest (red) via yellow
            color = self.interpolate_age_color(normalized_age)
            colors.append(color)

        return colors

    def interpolate_age_color(self, normalized_age):
        """Interpolate color from green (newest) to red (oldest) via yellow"""
        # Clamp to 0-1 range
        normalized_age = max(0, min(1, normalized_age))

        if normalized_age <= 0.5:
            # Green to Yellow (0.0 to 0.5)
            # Green: #218f3a (33, 143, 58)
            # Yellow: #8f8421 (143, 132, 33)
            t = normalized_age * 2  # Scale to 0-1 for this segment
            r = int(33 + (143 - 33) * t)
            g = int(143 + (132 - 143) * t)
            b = int(58 + (33 - 58) * t)
        else:
            # Yellow to Red (0.5 to 1.0)
            # Yellow: #8f8421 (143, 132, 33)
            # Red: #821d25 (130, 29, 37)
            t = (normalized_age - 0.5) * 2  # Scale to 0-1 for this segment
            r = int(143 + (130 - 143) * t)
            g = int(132 + (29 - 132) * t)
            b = int(33 + (37 - 33) * t)

        return f"#{r:02x}{g:02x}{b:02x}"

    def apply_age_based_row_colors(self, item_id, mr, background_color):
        """Apply age-based row styling with calculated background color"""
        # Create a unique tag name for this color
        tag_name = f"age_{background_color.replace('#', '')}"

        if tag_name not in self.configured_tags:
            # Calculate appropriate text color based on background brightness
            text_color = self.get_contrasting_text_color(background_color)

            self.mr_tree.tag_configure(tag_name,
                                       background=background_color,
                                       foreground=text_color)
            self.configured_tags.add(tag_name)

        # Apply the tag
        self.mr_tree.item(item_id, tags=(tag_name,))

    def get_contrasting_text_color(self, background_color):
        """Get contrasting text color (white or black) based on background brightness"""
        # Remove # and convert to RGB
        hex_color = background_color.lstrip('#')
        r = int(hex_color[0:2], 16)
        g = int(hex_color[2:4], 16)
        b = int(hex_color[4:6], 16)

        # Calculate brightness using luminance formula
        brightness = (r * 0.299 + g * 0.587 + b * 0.114)

        # Return white for dark backgrounds, black for light backgrounds
        return '#ffffff' if brightness < 128 else '#000000'

    def apply_simple_row_colors(self, item_id, mr):
        """Apply simple row styling based on MR state"""
        # Simple color scheme based on state
        if mr.state == 'opened':
            tag_name = 'opened'
            if tag_name not in self.configured_tags:
                self.mr_tree.tag_configure(tag_name,
                                           background='#2d2d2d',
                                           foreground='#ffffff')
                self.configured_tags.add(tag_name)
        elif mr.state == 'merged':
            tag_name = 'merged'
            if tag_name not in self.configured_tags:
                self.mr_tree.tag_configure(tag_name,
                                           background='#1e3a1e',
                                           foreground='#90ee90')
                self.configured_tags.add(tag_name)
        elif mr.state == 'closed':
            tag_name = 'closed'
            if tag_name not in self.configured_tags:
                self.mr_tree.tag_configure(tag_name,
                                           background='#3a1e1e',
                                           foreground='#ffaaaa')
                self.configured_tags.add(tag_name)
        else:
            tag_name = 'default'
            if tag_name not in self.configured_tags:
                self.mr_tree.tag_configure(tag_name,
                                           background='#2d2d2d',
                                           foreground='#ffffff')
                self.configured_tags.add(tag_name)

        # Apply the tag
        self.mr_tree.item(item_id, tags=(tag_name,))

    def format_date(self, date_str):
        """Format ISO date string to readable format with year"""
        try:
            dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            return dt.strftime('%m/%d/%y %H:%M')
        except:
            return date_str[:10]  # Fallback to date only

    def sort_treeview(self, column):
        """Sort treeview by column"""
        items = [(self.mr_tree.set(item, column), item) for item in self.mr_tree.get_children('')]

        # Determine sort order
        reverse = getattr(self, f'_sort_{column}_reverse', False)
        items.sort(reverse=reverse)
        setattr(self, f'_sort_{column}_reverse', not reverse)

        # Rearrange items
        for index, (_, item) in enumerate(items):
            self.mr_tree.move(item, '', index)

    def open_mr_in_browser(self, event=None):
        """Open selected MR in browser"""
        selection = self.mr_tree.selection()
        if not selection:
            return

        item = selection[0]
        mr_id = self.mr_tree.item(item, 'values')[0]

        # Find the MR object
        for mr in self.merge_requests:
            if str(mr.id) == str(mr_id):
                webbrowser.open(mr.web_url)
                break

    def copy_mr_url(self):
        """Copy selected MR URL to clipboard"""
        selection = self.mr_tree.selection()
        if not selection:
            return

        item = selection[0]
        mr_id = self.mr_tree.item(item, 'values')[0]

        # Find the MR object
        for mr in self.merge_requests:
            if str(mr.id) == str(mr_id):
                self.root.clipboard_clear()
                self.root.clipboard_append(mr.web_url)
                self.status_var.set("URL copied to clipboard")
                break

    def show_context_menu(self, event):
        """Show context menu on right-click"""
        # Select the item under cursor
        item = self.mr_tree.identify_row(event.y)
        if item:
            self.mr_tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)

    def toggle_auto_refresh(self):
        """Toggle auto-refresh functionality"""
        self.auto_refresh = self.auto_refresh_var.get()
        self.config_manager.set('auto_refresh', self.auto_refresh)

        if self.auto_refresh:
            self.start_auto_refresh()
            self.status_var.set("Auto-refresh enabled")
        else:
            self.stop_auto_refresh()
            self.status_var.set("Auto-refresh disabled")

    def start_auto_refresh(self):
        """Start auto-refresh timer"""
        if self.auto_refresh and self.gitlab_api:
            self.refresh_merge_requests()
            # Schedule next refresh
            self.root.after(self.refresh_interval * 1000, self.start_auto_refresh)

    def stop_auto_refresh(self):
        """Stop auto-refresh timer"""
        # Cancel any pending refresh
        pass  # tkinter's after() doesn't provide easy cancellation

    def run(self):
        """Start the application"""
        # Center window on screen
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

        # Start main loop
        self.root.mainloop()


def main():
    """Main entry point"""
    try:
        app = GitLabMRMonitor()
        app.run()
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
    except Exception as e:
        print(f"Application error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()