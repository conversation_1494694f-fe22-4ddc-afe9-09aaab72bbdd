#!/usr/bin/env python3
"""
Demo script showing how to use the self-contained Metrics Checker GUI.
This demonstrates that the GUI is completely independent and self-contained.
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import <PERSON><PERSON><PERSON>r

from check_metrics_gui import MetricsCheckerGUI


def demo_gui():
    """Demo the GUI with pre-filled data."""
    app = QApplication(sys.argv)
    
    # Create the main window
    window = MetricsCheckerGUI()
    
    # Pre-fill with demo data
    demo_json_path = os.path.join(os.path.dirname(__file__), "test_metrics.json")
    window.json_files_edit.setPlainText(demo_json_path)
    
    demo_metrics = """http_requests_total
cpu_usage_percent
memory_usage_bytes
low_cardinality_metric
http_request_total
cpu_usage_percentage
memory_usage_byte
some_completely_nonexistent_metric"""
    
    window.metrics_edit.setPlainText(demo_metrics)
    
    # Set some custom settings
    window.threshold_spin.setValue(0.7)  # Lower threshold for more suggestions
    window.max_suggestions_spin.setValue(3)  # Show fewer suggestions
    
    print("=== Metrics Checker GUI Demo ===")
    print("The GUI has been launched with demo data pre-filled:")
    print(f"- JSON file: {demo_json_path}")
    print("- Metrics: 6 sample metrics")
    print("- Threshold: 0.7 (lower for more suggestions)")
    print("- Max suggestions: 3")
    print("\nYou can:")
    print("1. Click 'Check Metrics' to see the results")
    print("2. Browse for your own JSON files")
    print("3. Add/remove metrics")
    print("4. Adjust settings")
    print("\nClose the window when done.")
    
    # Show the window
    window.show()
    
    # Start the event loop
    return app.exec()


if __name__ == '__main__':
    sys.exit(demo_gui())
