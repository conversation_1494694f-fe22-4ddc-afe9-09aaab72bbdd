# Metrics Checker GUI

A PyQt6-based graphical user interface for the metrics checker tool. This GUI version is **completely self-contained** and provides all the functionality of the command-line version with an intuitive, user-friendly interface. No dependencies on the original `check_metrics.py` file.

## Features

- **Dark Theme**: Natural, earthy color scheme with subtle green tinting and clean light text on dark backgrounds
- **Threaded Processing**: Non-blocking metrics checking using background threads
- **Tabbed Results**: Organized display of found metrics, similar metrics, and not found metrics
- **File Dialogs**: Easy browsing for JSON files and metrics files
- **Real-time Settings**: Adjustable similarity threshold and maximum suggestions
- **Recording Rules**: Automatic generation of recording rule group by expressions for high-cardinality metrics (max > 30)

## Requirements

```bash
pip install PyQt6
```

## Usage

### Running the GUI

```bash
python3 metrics_checker/check_metrics_gui.py
```

### Interface Overview

#### Left Panel - Input Controls

1. **JSON Report Files**
   - Enter JSON file paths manually (one per line)
   - Use "Browse JSON Files" button to select multiple files
   - Supports glob patterns and multiple file selection

2. **Metrics to Check**
   - Enter metric names manually (one per line)
   - Use "Load from File" button to import from a text file
   - Supports any number of metrics

3. **Settings**
   - **Similarity Threshold**: Controls how similar metric names need to be for suggestions (0.0-1.0)
   - **Max Suggestions**: Maximum number of similar metrics to show per not-found metric

4. **Check Metrics Button**
   - Starts the metrics checking process
   - Button is disabled during processing to prevent multiple concurrent checks

#### Right Panel - Results

The results are displayed in three tabs:

1. **Found Metrics Tab**
   - Shows metrics that were found in the JSON reports
   - Displays cardinality information (current, min, max, avg)
   - Shows non-core labels (excluding kubernetes_namespace, tenant_type, etc.)
   - **Recording rule group by expressions** for metrics with max cardinality > 30
   - Tab title shows count: "Found Metrics (X)"

2. **Similar Metrics Tab**
   - Shows metrics that weren't found but have similar names
   - Lists suggestions with similarity scores
   - Tab title shows count: "Similar Metrics (X)"

3. **Not Found Tab**
   - Shows metrics that weren't found and have no similar matches
   - Tab title shows count: "Not Found (X)"

### Example Workflow

1. Launch the GUI: `python3 metrics_checker/check_metrics_gui.py`
2. Click "Browse JSON Files" and select your JSON report files
3. Enter metric names in the "Metrics to Check" area, or use "Load from File"
4. Adjust settings if needed (threshold, max suggestions)
5. Click "Check Metrics"
6. Review results in the tabbed interface

### Recording Rules

The GUI automatically generates recording rule group by expressions for metrics with high cardinality (max > 30). These expressions include:

- All non-core labels present in the metric
- All core labels (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id)

Example output:
```
✓ http_requests_total
  Cardinality:
    - current: 1500
    - min: 800
    - max: 2000
    - avg: 1200
  Labels (non-core):
    - method: [GET, POST, PUT]
    - status_code: [200, 404, 500]
    - endpoint: [/api/users, /api/orders, /health]
  Recording rule group by: by (endpoint, kubernetes_namespace, lcaas_id, method, namespace, product_tier, product_type, status_code, tenant_type, xdr_id)
```

## Comparison with Command Line Version

| Feature | Command Line | GUI |
|---------|-------------|-----|
| JSON file input | Command line arguments | File browser + text input |
| Metrics input | Command line arguments or file | Text area + file loader |
| Results display | Colored terminal output | Organized tabs |
| Settings | Command line flags | Interactive controls |
| User experience | Technical users | All users |
| Concurrent processing | Single-threaded | Multi-threaded |
| Recording rules | Text output | Formatted display |

## Technical Details

- **Framework**: PyQt6
- **Threading**: Uses QThread for non-blocking metrics processing
- **Theme**: Custom dark theme with earthy colors
- **Font**: Monospace font for results (falls back if Consolas not available)
- **Architecture**: Self-contained with all core functionality embedded
- **Dependencies**: Only requires PyQt6 - no imports from other project files

## Troubleshooting

### Font Warning
If you see a warning about "Consolas" font, it's harmless. The GUI will use the system's default monospace font.

### PyQt6 Installation Issues
If PyQt6 installation fails, try:
```bash
pip install --upgrade pip
pip install PyQt6
```

### File Not Found Errors
Make sure your JSON report files exist and are accessible. The GUI will show clear error messages if files cannot be found or loaded.
