#!/usr/bin/env python3
"""
GUI version of the metrics checker using PyQt6.
Provides all functionality of the command-line version with a user-friendly interface.
"""

import sys
import json
import os
import glob
from typing import Dict, List, Tuple, Optional
from difflib import SequenceMatcher
from PyQt6.QtWidgets import (
    QA<PERSON>lication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QTextEdit, QFileDialog,
    QGroupBox, QSpinBox, QDoubleSpinBox, QCheckBox, QSplitter,
    QTabWidget, QScrollArea, QFrame
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QTextCharFormat, QColor, QPalette

# Labels to exclude from display (non-core labels preference)
EXCLUDED_LABELS = {
    'kubernetes_namespace',
    'tenant_type',
    'product_type',
    'product_tier',
    'xdr_id',
    'namespace',
    'lcaas_id',
    'product_code',
}


def load_reports(json_paths: List[str]) -> Tuple[Dict[str, dict], List[str]]:
    """Load one or more JSON reports and merge entries by metric name.
    If a metric appears in multiple files, the first occurrence wins unless a later
    occurrence has a 'cardinality' block while the first does not, in which case we keep
    the one with cardinality.
    Supports glob patterns (e.g., reports/*.json) whether or not the shell expands them.
    """
    name_to_entry: Dict[str, dict] = {}
    names_in_order: List[str] = []

    # Expand any glob patterns to actual file paths
    files_to_read: List[str] = []
    for pattern in json_paths:
        matches = glob.glob(pattern)
        if matches:
            files_to_read.extend(matches)
        elif os.path.exists(pattern):
            files_to_read.append(pattern)
        # else: silently ignore missing pattern; we'll error if none found overall

    if not files_to_read:
        raise FileNotFoundError(
            f"No JSON files found for patterns: {', '.join(json_paths)}"
        )

    for json_path in files_to_read:
        with open(json_path, 'r') as f:
            data = json.load(f)
        if not isinstance(data, list):
            raise ValueError(f"Expected JSON to be a list of metric objects: {json_path}")
        for entry in data:
            name = entry.get('name')
            if not name:
                continue
            if name not in name_to_entry:
                name_to_entry[name] = entry
                names_in_order.append(name)
            else:
                # Prefer an entry that contains 'cardinality' if existing one doesn't
                has_card_new = bool(entry.get('cardinality'))
                has_card_old = bool(name_to_entry[name].get('cardinality'))
                if has_card_new and not has_card_old:
                    name_to_entry[name] = entry
    return name_to_entry, names_in_order


def compute_similarity(a: str, b: str) -> float:
    # Combine ratio with simple prefix/contain heuristics
    a_low, b_low = a.lower(), b.lower()
    ratio = SequenceMatcher(None, a_low, b_low).ratio()
    if a_low in b_low or b_low in a_low:
        ratio = max(ratio, 0.85)
    # underscore-aware: boost if initial segments match
    a_parts, b_parts = a_low.split('_'), b_low.split('_')
    common = 0
    for x, y in zip(a_parts, b_parts):
        if x == y:
            common += 1
        else:
            break
    if common >= 1:
        ratio = max(ratio, 0.7 + min(0.2, common * 0.05))
    return ratio


def find_similar(name: str, candidates: List[str], threshold: float = 0.8, max_suggestions: int = 5) -> List[Tuple[str, float]]:
    scores = []
    for cand in candidates:
        if cand == name:
            continue
        s = compute_similarity(name, cand)
        if s > threshold:  # strictly greater than threshold
            scores.append((cand, s))
    scores.sort(key=lambda x: x[1], reverse=True)
    return scores[:max_suggestions]


def generate_recording_rule_group_by(entry: dict) -> str:
    """Generate a recording rule group by expression with all labels (non-core + all core labels)."""
    labels = entry.get('labels') or {}

    # Get non-core labels (excluding the predefined set)
    non_core_labels = [k for k in labels.keys() if k not in EXCLUDED_LABELS]

    # Always include all core labels (excluded labels), regardless of presence in metric
    core_labels = list(EXCLUDED_LABELS)

    # Combine all labels: non-core first, then all core labels
    all_labels = non_core_labels + core_labels

    if not all_labels:
        return "by ()"

    # Sort for consistent output
    all_labels.sort()

    return f"by ({', '.join(all_labels)})"


class MetricsWorker(QThread):
    """Worker thread for processing metrics to avoid blocking the GUI."""
    
    results_ready = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, json_paths: List[str], metrics: List[str], 
                 threshold: float, max_suggestions: int):
        super().__init__()
        self.json_paths = json_paths
        self.metrics = metrics
        self.threshold = threshold
        self.max_suggestions = max_suggestions
    
    def run(self):
        try:
            # Load reports
            name_to_entry, all_names = load_reports(self.json_paths)
            
            found = []  # (name, entry)
            similar = []  # (name, [(cand, score), ...])
            not_found = []  # [name]
            
            name_set = set(all_names)
            
            for m in self.metrics:
                if m in name_set:
                    found.append((m, name_to_entry[m]))
                else:
                    suggestions = find_similar(
                        m, all_names, 
                        threshold=self.threshold, 
                        max_suggestions=self.max_suggestions
                    )
                    if suggestions:
                        similar.append((m, suggestions))
                    else:
                        not_found.append(m)
            
            results = {
                'found': found,
                'similar': similar,
                'not_found': not_found
            }
            
            self.results_ready.emit(results)
            
        except Exception as e:
            self.error_occurred.emit(str(e))


class MetricsCheckerGUI(QMainWindow):
    """Main GUI application for the metrics checker."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Metrics Checker - GUI Version")
        self.setGeometry(100, 100, 1200, 800)
        
        # Apply dark theme
        self.apply_dark_theme()
        
        # Initialize UI
        self.init_ui()
        
        # Worker thread
        self.worker = None
    
    def apply_dark_theme(self):
        """Apply a dark theme with natural, earthy colors and subtle green tinting."""
        palette = QPalette()
        
        # Dark background colors
        palette.setColor(QPalette.ColorRole.Window, QColor(45, 52, 54))
        palette.setColor(QPalette.ColorRole.WindowText, QColor(220, 221, 225))
        palette.setColor(QPalette.ColorRole.Base, QColor(35, 39, 42))
        palette.setColor(QPalette.ColorRole.AlternateBase, QColor(55, 61, 63))
        palette.setColor(QPalette.ColorRole.ToolTipBase, QColor(35, 39, 42))
        palette.setColor(QPalette.ColorRole.ToolTipText, QColor(220, 221, 225))
        palette.setColor(QPalette.ColorRole.Text, QColor(220, 221, 225))
        palette.setColor(QPalette.ColorRole.Button, QColor(55, 61, 63))
        palette.setColor(QPalette.ColorRole.ButtonText, QColor(220, 221, 225))
        palette.setColor(QPalette.ColorRole.BrightText, QColor(255, 255, 255))
        palette.setColor(QPalette.ColorRole.Link, QColor(129, 236, 236))
        palette.setColor(QPalette.ColorRole.Highlight, QColor(85, 170, 85))
        palette.setColor(QPalette.ColorRole.HighlightedText, QColor(35, 39, 42))
        
        self.setPalette(palette)
    
    def init_ui(self):
        """Initialize the user interface."""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Create splitter for input and output
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # Left panel - Input controls
        input_panel = self.create_input_panel()
        splitter.addWidget(input_panel)
        
        # Right panel - Results
        results_panel = self.create_results_panel()
        splitter.addWidget(results_panel)
        
        # Set splitter proportions
        splitter.setSizes([400, 800])
    
    def create_input_panel(self) -> QWidget:
        """Create the input panel with all controls."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # JSON Files section
        json_group = QGroupBox("JSON Report Files")
        json_layout = QVBoxLayout(json_group)
        
        self.json_files_edit = QTextEdit()
        self.json_files_edit.setMaximumHeight(100)
        self.json_files_edit.setPlaceholderText("Enter JSON file paths (one per line) or use Browse button")
        json_layout.addWidget(self.json_files_edit)
        
        browse_btn = QPushButton("Browse JSON Files")
        browse_btn.clicked.connect(self.browse_json_files)
        json_layout.addWidget(browse_btn)
        
        layout.addWidget(json_group)
        
        # Metrics section
        metrics_group = QGroupBox("Metrics to Check")
        metrics_layout = QVBoxLayout(metrics_group)
        
        self.metrics_edit = QTextEdit()
        self.metrics_edit.setMaximumHeight(100)
        self.metrics_edit.setPlaceholderText("Enter metric names (one per line)")
        metrics_layout.addWidget(self.metrics_edit)
        
        metrics_file_btn = QPushButton("Load from File")
        metrics_file_btn.clicked.connect(self.load_metrics_file)
        metrics_layout.addWidget(metrics_file_btn)
        
        layout.addWidget(metrics_group)
        
        # Settings section
        settings_group = QGroupBox("Settings")
        settings_layout = QVBoxLayout(settings_group)
        
        # Threshold
        threshold_layout = QHBoxLayout()
        threshold_layout.addWidget(QLabel("Similarity Threshold:"))
        self.threshold_spin = QDoubleSpinBox()
        self.threshold_spin.setRange(0.0, 1.0)
        self.threshold_spin.setSingleStep(0.1)
        self.threshold_spin.setValue(0.8)
        threshold_layout.addWidget(self.threshold_spin)
        settings_layout.addLayout(threshold_layout)
        
        # Max suggestions
        suggestions_layout = QHBoxLayout()
        suggestions_layout.addWidget(QLabel("Max Suggestions:"))
        self.max_suggestions_spin = QSpinBox()
        self.max_suggestions_spin.setRange(1, 20)
        self.max_suggestions_spin.setValue(5)
        suggestions_layout.addWidget(self.max_suggestions_spin)
        settings_layout.addLayout(suggestions_layout)
        
        layout.addWidget(settings_group)
        
        # Check button
        self.check_btn = QPushButton("Check Metrics")
        self.check_btn.clicked.connect(self.check_metrics)
        layout.addWidget(self.check_btn)
        
        # Add stretch to push everything to top
        layout.addStretch()
        
        return panel
    
    def create_results_panel(self) -> QWidget:
        """Create the results panel with tabs for different result types."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Results tabs
        self.results_tabs = QTabWidget()
        layout.addWidget(self.results_tabs)
        
        # Found metrics tab
        self.found_text = QTextEdit()
        self.found_text.setReadOnly(True)
        self.found_text.setFont(QFont("Consolas", 14))
        self.results_tabs.addTab(self.found_text, "Found Metrics")

        # Similar metrics tab
        self.similar_text = QTextEdit()
        self.similar_text.setReadOnly(True)
        self.similar_text.setFont(QFont("Consolas", 14))
        self.results_tabs.addTab(self.similar_text, "Similar Metrics")

        # Not found tab
        self.not_found_text = QTextEdit()
        self.not_found_text.setReadOnly(True)
        self.not_found_text.setFont(QFont("Consolas", 14))
        self.results_tabs.addTab(self.not_found_text, "Not Found")
        
        return panel

    def browse_json_files(self):
        """Open file dialog to select JSON files."""
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "Select JSON Report Files",
            "",
            "JSON Files (*.json);;All Files (*)"
        )
        if files:
            current_text = self.json_files_edit.toPlainText().strip()
            if current_text:
                current_text += "\n"
            current_text += "\n".join(files)
            self.json_files_edit.setPlainText(current_text)

    def load_metrics_file(self):
        """Load metrics from a text file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Metrics File",
            "",
            "Text Files (*.txt);;All Files (*)"
        )
        if file_path:
            try:
                with open(file_path, 'r') as f:
                    metrics = [line.strip() for line in f if line.strip()]
                current_text = self.metrics_edit.toPlainText().strip()
                if current_text:
                    current_text += "\n"
                current_text += "\n".join(metrics)
                self.metrics_edit.setPlainText(current_text)
            except Exception as e:
                self.show_error(f"Error loading metrics file: {e}")

    def check_metrics(self):
        """Start the metrics checking process."""
        # Get input data
        json_paths = [
            path.strip() for path in self.json_files_edit.toPlainText().split('\n')
            if path.strip()
        ]
        metrics = [
            metric.strip() for metric in self.metrics_edit.toPlainText().split('\n')
            if metric.strip()
        ]

        # Validate inputs
        if not json_paths:
            self.show_error("Please specify at least one JSON file.")
            return

        if not metrics:
            self.show_error("Please specify at least one metric to check.")
            return

        # Disable check button and clear results
        self.check_btn.setEnabled(False)
        self.check_btn.setText("Checking...")
        self.clear_results()

        # Start worker thread
        self.worker = MetricsWorker(
            json_paths,
            metrics,
            self.threshold_spin.value(),
            self.max_suggestions_spin.value()
        )
        self.worker.results_ready.connect(self.display_results)
        self.worker.error_occurred.connect(self.show_error)
        self.worker.finished.connect(self.on_worker_finished)
        self.worker.start()

    def clear_results(self):
        """Clear all result tabs."""
        self.found_text.clear()
        self.similar_text.clear()
        self.not_found_text.clear()

    def on_worker_finished(self):
        """Re-enable the check button when worker finishes."""
        self.check_btn.setEnabled(True)
        self.check_btn.setText("Check Metrics")

    def show_error(self, message: str):
        """Display error message in all result tabs."""
        error_text = f"Error: {message}"
        self.found_text.setPlainText(error_text)
        self.similar_text.setPlainText(error_text)
        self.not_found_text.setPlainText(error_text)
        self.on_worker_finished()

    def display_results(self, results: dict):
        """Display the results in the appropriate tabs."""
        found = results['found']
        similar = results['similar']
        not_found = results['not_found']

        # Display found metrics
        self.display_found_metrics(found)

        # Display similar metrics
        self.display_similar_metrics(similar)

        # Display not found metrics
        self.display_not_found_metrics(not_found)

        # Update tab titles with counts
        self.results_tabs.setTabText(0, f"Found Metrics ({len(found)})")
        self.results_tabs.setTabText(1, f"Similar Metrics ({len(similar)})")
        self.results_tabs.setTabText(2, f"Not Found ({len(not_found)})")

    def display_found_metrics(self, found: List[Tuple[str, dict]]):
        """Display found metrics with their details."""
        if not found:
            self.found_text.setPlainText("No metrics found.")
            return

        # Use HTML for colored text
        html_output = []
        html_output.append('<html><body style="font-family: monospace; font-size: 14pt; color: #dcdde1; background-color: #23272a;">')

        for metric_name, entry in found:
            # Get cardinality to determine color
            card = entry.get('cardinality')
            max_cardinality = card.get('max', 0) if card else 0

            # Color metric name based on cardinality max
            if max_cardinality > 30:
                color = "#e74c3c"  # Red for high cardinality
            else:
                color = "#27ae60"  # Green for low cardinality

            html_output.append(f'<p style="margin: 0; padding: 2px 0;"><span style="color: {color}; font-weight: bold;">✓ {metric_name}</span></p>')

            # Cardinality
            if card:
                html_output.append('<p style="margin: 0; padding-left: 20px;">Cardinality:</p>')
                for k in ['per_pod', 'current', 'min', 'max', 'avg']:
                    if k in card:
                        html_output.append(f'<p style="margin: 0; padding-left: 40px;">- {k}: {card[k]}</p>')
            else:
                html_output.append('<p style="margin: 0; padding-left: 20px;">Cardinality: (not available)</p>')

            # Non-core labels
            labels = entry.get('labels') or {}
            filtered_labels = {k: v for k, v in labels.items() if k not in EXCLUDED_LABELS}
            if filtered_labels:
                html_output.append('<p style="margin: 0; padding-left: 20px; color: #f1c40f;">Labels (non-core):</p>')
                for key, vals in filtered_labels.items():
                    if isinstance(vals, list):
                        display_vals = vals
                    elif vals is None:
                        display_vals = []
                    else:
                        display_vals = [vals]

                    if len(display_vals) <= 5:
                        joined = ", ".join(str(x) for x in display_vals)
                        html_output.append(f'<p style="margin: 0; padding-left: 40px; color: #f1c40f;">- {key}: [{joined}]</p>')
                    else:
                        preview = ", ".join(str(x) for x in display_vals[:5])
                        html_output.append(f'<p style="margin: 0; padding-left: 40px; color: #f1c40f;">- {key}: [{preview}, ...] ({len(display_vals)} values)</p>')
            else:
                html_output.append('<p style="margin: 0; padding-left: 20px; color: #f1c40f;">Labels (non-core): none</p>')

            # Recording rule (only if max cardinality > 30)
            if card and card.get('max', 0) > 30:
                group_by_expr = generate_recording_rule_group_by(entry)
                html_output.append(f'<p style="margin: 0; padding-left: 20px; color: #81ecec;">Recording rule group by: {group_by_expr}</p>')

            html_output.append('<br>')  # Empty line between metrics

        html_output.append('</body></html>')

        self.found_text.setHtml("".join(html_output))

    def display_similar_metrics(self, similar: List[Tuple[str, List[Tuple[str, float]]]]):
        """Display similar metrics with suggestions."""
        if not similar:
            self.similar_text.setPlainText("No similar metrics found.")
            return

        # Use HTML for colored text
        html_output = []
        html_output.append('<html><body style="font-family: monospace; font-size: 14pt; color: #dcdde1; background-color: #23272a;">')

        for metric_name, suggestions in similar:
            # Display metric name in yellow
            html_output.append(f'<p style="margin: 0; padding: 2px 0;"><span style="color: #f1c40f; font-weight: bold;">{metric_name}</span></p>')
            html_output.append('<p style="margin: 0; padding-left: 20px;">Suggestions:</p>')

            for suggestion, score in suggestions:
                # Suggestion in blue, similarity score in green
                html_output.append(f'<p style="margin: 0; padding-left: 40px;">→ <span style="color: #3498db;">{suggestion}</span> <span style="color: #27ae60;">(similarity: {score:.3f})</span></p>')

            html_output.append('<br>')  # Empty line between metrics

        html_output.append('</body></html>')

        self.similar_text.setHtml("".join(html_output))

    def display_not_found_metrics(self, not_found: List[str]):
        """Display metrics that were not found at all."""
        if not not_found:
            self.not_found_text.setPlainText("All metrics were found or had similar matches.")
            return

        # Use HTML for colored text
        html_output = []
        html_output.append('<html><body style="font-family: monospace; font-size: 14pt; color: #dcdde1; background-color: #23272a;">')

        for metric_name in not_found:
            # Display metric name in red
            html_output.append(f'<p style="margin: 0; padding: 2px 0;"><span style="color: #e74c3c; font-weight: bold;">✗ {metric_name}</span></p>')

        html_output.append('</body></html>')

        self.not_found_text.setHtml("".join(html_output))


def main():
    """Main entry point for the GUI application."""
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("Metrics Checker GUI")
    app.setApplicationVersion("1.0")

    # Create and show main window
    window = MetricsCheckerGUI()
    window.show()

    # Start event loop
    sys.exit(app.exec())


if __name__ == '__main__':
    main()
