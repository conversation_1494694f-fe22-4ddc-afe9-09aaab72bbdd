[{"name": "http_requests_total", "cardinality": {"current": 1500, "min": 800, "max": 2000, "avg": 1200}, "labels": {"method": ["GET", "POST", "PUT"], "status_code": ["200", "404", "500"], "endpoint": ["/api/users", "/api/orders", "/health"], "kubernetes_namespace": ["production", "staging"], "tenant_type": ["premium", "basic"], "product_type": ["web", "mobile"], "product_tier": ["gold", "silver"], "xdr_id": ["xdr-001", "xdr-002"], "namespace": ["default", "kube-system"], "lcaas_id": ["lcaas-123", "lcaas-456"]}}, {"name": "cpu_usage_percent", "cardinality": {"current": 800, "min": 400, "max": 1200, "avg": 750}, "labels": {"instance": ["server-1", "server-2", "server-3"], "region": ["us-east-1", "us-west-2"], "kubernetes_namespace": ["production"], "tenant_type": ["premium"], "product_type": ["web"]}}, {"name": "memory_usage_bytes", "cardinality": {"current": 600, "min": 300, "max": 900, "avg": 550}, "labels": {"container": ["app", "sidecar"], "pod": ["pod-1", "pod-2"], "lcaas_id": ["lcaas-789"]}}, {"name": "low_cardinality_metric", "cardinality": {"current": 15, "min": 10, "max": 25, "avg": 18}, "labels": {"service": ["api", "web"], "environment": ["prod", "staging"], "kubernetes_namespace": ["production"]}}]