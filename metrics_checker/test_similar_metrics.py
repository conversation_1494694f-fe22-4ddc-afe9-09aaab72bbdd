#!/usr/bin/env python3
"""
Test script to verify the similar metrics functionality with color coding.
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QEvent<PERSON>oop

from check_metrics_gui import MetricsCheckerGUI, MetricsWorker


def test_similar_metrics():
    """Test the similar metrics functionality."""
    app = QApplication(sys.argv)
    
    # Set up test data with metrics that should generate similar matches
    test_json_path = os.path.join(os.path.dirname(__file__), "test_metrics.json")
    test_metrics = [
        "http_requests_total",      # Exact match
        "http_request_total",       # Similar to http_requests_total
        "cpu_usage_percentage",     # Similar to cpu_usage_percent
        "memory_usage_byte",        # Similar to memory_usage_bytes
        "completely_nonexistent_metric"  # No similar matches
    ]
    
    print("Testing similar metrics functionality...")
    print(f"JSON file: {test_json_path}")
    print(f"Metrics to check: {test_metrics}")
    
    # Test the worker directly
    worker = MetricsWorker([test_json_path], test_metrics, 0.7, 3)
    
    # Set up event loop to wait for results
    loop = QEventLoop()
    results = {}
    
    def on_results(result_data):
        nonlocal results
        results = result_data
        loop.quit()
    
    def on_error(error_msg):
        print(f"Error: {error_msg}")
        loop.quit()
    
    worker.results_ready.connect(on_results)
    worker.error_occurred.connect(on_error)
    
    # Start worker and wait for completion
    worker.start()
    loop.exec()
    
    # Display results
    if results:
        found = results.get('found', [])
        similar = results.get('similar', [])
        not_found = results.get('not_found', [])
        
        print(f"\n=== RESULTS ===")
        print(f"Found: {len(found)} metrics")
        for metric_name, entry in found:
            print(f"  ✓ {metric_name}")
        
        print(f"\nSimilar: {len(similar)} metrics")
        for metric_name, suggestions in similar:
            print(f"  Not Found: {metric_name}")
            for suggestion, score in suggestions:
                print(f"    → {suggestion} (similarity: {score:.3f})")
        
        print(f"\nNot found: {len(not_found)} metrics")
        for metric_name in not_found:
            print(f"  ✗ {metric_name}")
        
        print("\n=== SIMILAR METRICS TEST COMPLETED ===")
        print("In the GUI, you should see:")
        print("- Metric names in YELLOW")
        print("- Suggestion names in BLUE")
        print("- Similarity scores in GREEN")
    else:
        print("No results received!")
    
    app.quit()
    return 0


if __name__ == '__main__':
    sys.exit(test_similar_metrics())
