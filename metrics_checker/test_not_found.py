#!/usr/bin/env python3
"""
Test script to verify the not found metrics functionality with red color coding.
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QEventLoop

from check_metrics_gui import MetricsCheckerGUI, MetricsWorker


def test_not_found_metrics():
    """Test the not found metrics functionality."""
    app = QApplication(sys.argv)
    
    # Set up test data with metrics from the user's merrics.txt file
    test_json_path = os.path.join(os.path.dirname(__file__), "test_metrics.json")
    test_metrics = [
        "http_requests_total",  # This exists
        "cloud_assets_collection_num_rit_failures_total",  # From user's file - won't be found
        "cloud_assets_collection_num_handle_account_failures_total",  # From user's file - won't be found
        "xdr_generate_notification_error_total",  # From user's file - won't be found
        "xdr_generate_notification_error_created",  # From user's file - won't be found
        "completely_unknown_metric"  # This definitely won't be found
    ]
    
    print("Testing not found metrics functionality...")
    print(f"JSON file: {test_json_path}")
    print(f"Metrics to check: {test_metrics}")
    
    # Test the worker directly
    worker = MetricsWorker([test_json_path], test_metrics, 0.8, 3)
    
    # Set up event loop to wait for results
    loop = QEventLoop()
    results = {}
    
    def on_results(result_data):
        nonlocal results
        results = result_data
        loop.quit()
    
    def on_error(error_msg):
        print(f"Error: {error_msg}")
        loop.quit()
    
    worker.results_ready.connect(on_results)
    worker.error_occurred.connect(on_error)
    
    # Start worker and wait for completion
    worker.start()
    loop.exec()
    
    # Display results
    if results:
        found = results.get('found', [])
        similar = results.get('similar', [])
        not_found = results.get('not_found', [])
        
        print(f"\n=== RESULTS ===")
        print(f"Found: {len(found)} metrics")
        for metric_name, entry in found:
            print(f"  ✓ {metric_name}")
        
        print(f"\nSimilar: {len(similar)} metrics")
        for metric_name, suggestions in similar:
            print(f"  ? {metric_name}")
            for suggestion, score in suggestions[:2]:  # Show top 2
                print(f"    → {suggestion} (similarity: {score:.3f})")
        
        print(f"\nNot found: {len(not_found)} metrics")
        for metric_name in not_found:
            print(f"  ✗ {metric_name}")
        
        print("\n=== NOT FOUND METRICS TEST COMPLETED ===")
        print("In the GUI 'Not Found' tab, you should see:")
        print("- All metric names in RED with ✗ symbol")
        print("- Clean, bold formatting for easy identification")
        
        if not_found:
            print(f"\nExpected {len(not_found)} metrics to be displayed in red in the GUI")
    else:
        print("No results received!")
    
    app.quit()
    return 0


if __name__ == '__main__':
    sys.exit(test_not_found_metrics())
