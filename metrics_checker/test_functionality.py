#!/usr/bin/env python3
"""
Test script to verify the core functionality of the self-contained GUI.
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer, QEvent<PERSON>oop

from check_metrics_gui import MetricsCheckerGUI, MetricsWorker


def test_core_functionality():
    """Test the core metrics checking functionality."""
    app = QApplication(sys.argv)
    
    # Create the main window
    window = MetricsCheckerGUI()
    
    # Set up test data
    test_json_path = os.path.join(os.path.dirname(__file__), "test_metrics.json")
    test_metrics = [
        "http_requests_total",
        "cpu_usage_percent", 
        "memory_usage_bytes",
        "low_cardinality_metric",
        "nonexistent_metric"
    ]
    
    print("Testing core functionality...")
    print(f"JSON file: {test_json_path}")
    print(f"Metrics to check: {test_metrics}")
    
    # Test the worker directly
    worker = MetricsWorker([test_json_path], test_metrics, 0.8, 5)
    
    # Set up event loop to wait for results
    loop = QEventLoop()
    results = {}
    
    def on_results(result_data):
        nonlocal results
        results = result_data
        loop.quit()
    
    def on_error(error_msg):
        print(f"Error: {error_msg}")
        loop.quit()
    
    worker.results_ready.connect(on_results)
    worker.error_occurred.connect(on_error)
    
    # Start worker and wait for completion
    worker.start()
    loop.exec()
    
    # Display results
    if results:
        found = results.get('found', [])
        similar = results.get('similar', [])
        not_found = results.get('not_found', [])
        
        print(f"\n=== RESULTS ===")
        print(f"Found: {len(found)} metrics")
        for metric_name, entry in found:
            print(f"  ✓ {metric_name}")
            card = entry.get('cardinality', {})
            max_card = card.get('max', 0)
            print(f"    Max cardinality: {max_card}")
            if max_card > 30:
                from check_metrics_gui import generate_recording_rule_group_by
                group_by = generate_recording_rule_group_by(entry)
                print(f"    Recording rule: {group_by}")
        
        print(f"\nSimilar: {len(similar)} metrics")
        for metric_name, suggestions in similar:
            print(f"  ? {metric_name}")
            for suggestion, score in suggestions[:2]:  # Show top 2
                print(f"    → {suggestion} (score: {score:.3f})")
        
        print(f"\nNot found: {len(not_found)} metrics")
        for metric_name in not_found:
            print(f"  ✗ {metric_name}")
        
        print("\n=== TEST COMPLETED SUCCESSFULLY ===")
    else:
        print("No results received!")
    
    app.quit()
    return 0


if __name__ == '__main__':
    sys.exit(test_core_functionality())
