#!/usr/bin/env python3
"""
Test script to verify the GUI functionality programmatically.
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# Import the GUI module directly
from check_metrics_gui import MetricsCheckerGUI


def test_gui():
    """Test the GUI functionality."""
    app = QApplication(sys.argv)
    
    # Create the main window
    window = MetricsCheckerGUI()
    
    # Set up test data
    test_json_path = os.path.join(os.path.dirname(__file__), "test_metrics.json")
    window.json_files_edit.setPlainText(test_json_path)
    
    test_metrics = """http_requests_total
cpu_usage_percent
memory_usage_bytes
low_cardinality_metric
nonexistent_metric"""
    window.metrics_edit.setPlainText(test_metrics)
    
    # Show the window
    window.show()
    
    # Set up a timer to automatically close the application after a few seconds
    def close_app():
        print("GUI test completed successfully!")
        print("Window title:", window.windowTitle())
        print("JSON files input:", window.json_files_edit.toPlainText().strip())
        print("Metrics input:", len(window.metrics_edit.toPlainText().strip().split('\n')), "metrics")
        print("Threshold:", window.threshold_spin.value())
        print("Max suggestions:", window.max_suggestions_spin.value())
        app.quit()
    
    # Close after 3 seconds
    QTimer.singleShot(3000, close_app)
    
    # Start the event loop
    return app.exec()


if __name__ == '__main__':
    sys.exit(test_gui())
