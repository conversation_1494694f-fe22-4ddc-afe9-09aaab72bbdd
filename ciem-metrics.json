[{"name": "ciem_timer_rule_management_filter_rules_seconds_count", "labels": {"MODULE": ["DSPM", "CIEM"], "POLICIES_COUNT": ["2", "3"], "RULES_COUNT": ["26", "56"], "outcome": ["SUCCESS"], "app": ["xdr-st-9997725782356-ciem-rule-scanner-dspm", "xdr-st-9997725782356-ciem-rule-scanner-ciem"]}, "warnings": ["HPA is not defined for 'ciem-rule-scanner-dspm' (xdr-st-9997725782356-ciem-rule-scanner-dspm)", "HPA is not defined for 'ciem-rule-scanner-ciem' (xdr-st-9997725782356-ciem-rule-scanner-ciem)"], "hpa": {"ciem-rule-scanner-dspm": {"min": 1, "max": 1}, "ciem-rule-scanner-ciem": {"min": 1, "max": 1}}, "targets": [{"scrapeUrl": "http://10.12.9.39:8080/actuator/prometheus", "pod_name": "xdr-st-9997725782356-ciem-rule-scanner-dspm-6c69dd98f4-7zsn8", "app": "xdr-st-9997725782356-ciem-rule-scanner-dspm", "raw_metric": ["ciem_timer_rule_management_filter_rules_seconds_count{MODULE=\"DSPM\",POLICIES_COUNT=\"2\",RULES_COUNT=\"26\",outcome=\"SUCCESS\"} 1", "ciem_timer_rule_management_filter_rules_seconds_count{MODULE=\"DSPM\",POLICIES_COUNT=\"3\",RULES_COUNT=\"26\",outcome=\"SUCCESS\"} 6"], "image": [{"container": "xdr-st-9997725782356-ciem-rule-scanner-dspm", "image": "us-docker.pkg.dev/xdr-registry-dev-01/prismacloud/iam:CIEM-00-check-compliance-scan-log-c491a10267707db217062cd19558d7a6510abd32"}]}, {"scrapeUrl": "http://10.12.9.135:8080/actuator/prometheus", "pod_name": "xdr-st-9997725782356-ciem-rule-scanner-ciem-5664f96f58-w7kht", "app": "xdr-st-9997725782356-ciem-rule-scanner-ciem", "raw_metric": ["ciem_timer_rule_management_filter_rules_seconds_count{MODULE=\"CIEM\",POLICIES_COUNT=\"3\",RULES_COUNT=\"56\",outcome=\"SUCCESS\"} 3"], "image": [{"container": "xdr-st-9997725782356-ciem-rule-scanner-ciem", "image": "us-docker.pkg.dev/xdr-registry-dev-01/prismacloud/iam:CIEM-00-check-compliance-scan-log-483be62b647ac4ff0f76a37d006cfbfdddf41113"}]}], "current_ts_count": 3, "cardinality_formula": {"per_pod": "2(MODULE labels) * 2(POLICIES_COUNT labels) * 2(RULES_COUNT labels) * 1(outcome labels) * 2(app labels)", "current": "2(MODULE labels) * 2(POLICIES_COUNT labels) * 2(RULES_COUNT labels) * 1(outcome labels) * 2(app labels) * 2(targets)", "min": "2(MODULE labels) * 2(POLICIES_COUNT labels) * 2(RULES_COUNT labels) * 1(outcome labels) * 2(app labels) * (1(ciem-rule-scanner-dspm pods) + 1(ciem-rule-scanner-ciem pods))", "max": "2(MODULE labels) * 2(POLICIES_COUNT labels) * 2(RULES_COUNT labels) * 1(outcome labels) * 2(app labels) * (1(ciem-rule-scanner-dspm pods) + 1(ciem-rule-scanner-ciem pods))", "avg": "((2(MODULE labels) * 2(POLICIES_COUNT labels) * 2(RULES_COUNT labels) * 1(outcome labels) * 2(app labels) * (1(ciem-rule-scanner-dspm pods) + 1(ciem-rule-scanner-ciem pods))) + (2(MODULE labels) * 2(POLICIES_COUNT labels) * 2(RULES_COUNT labels) * 1(outcome labels) * 2(app labels) * (1(ciem-rule-scanner-dspm pods) + 1(ciem-rule-scanner-ciem pods)))) / 2"}, "cardinality": {"per_pod": 16, "current": 32, "min": 32, "max": 32, "avg": 32.0}}, {"name": "ciem_timer_rule_management_filter_rules_seconds_sum", "labels": {"MODULE": ["DSPM", "CIEM"], "POLICIES_COUNT": ["2", "3"], "RULES_COUNT": ["26", "56"], "outcome": ["SUCCESS"], "app": ["xdr-st-9997725782356-ciem-rule-scanner-dspm", "xdr-st-9997725782356-ciem-rule-scanner-ciem"]}, "warnings": ["HPA is not defined for 'ciem-rule-scanner-dspm' (xdr-st-9997725782356-ciem-rule-scanner-dspm)", "HPA is not defined for 'ciem-rule-scanner-ciem' (xdr-st-9997725782356-ciem-rule-scanner-ciem)"], "hpa": {"ciem-rule-scanner-dspm": {"min": 1, "max": 1}, "ciem-rule-scanner-ciem": {"min": 1, "max": 1}}, "targets": [{"scrapeUrl": "http://10.12.9.39:8080/actuator/prometheus", "pod_name": "xdr-st-9997725782356-ciem-rule-scanner-dspm-6c69dd98f4-7zsn8", "app": "xdr-st-9997725782356-ciem-rule-scanner-dspm", "raw_metric": ["ciem_timer_rule_management_filter_rules_seconds_sum{MODULE=\"DSPM\",POLICIES_COUNT=\"2\",RULES_COUNT=\"26\",outcome=\"SUCCESS\"} 2.785883835", "ciem_timer_rule_management_filter_rules_seconds_sum{MODULE=\"DSPM\",POLICIES_COUNT=\"3\",RULES_COUNT=\"26\",outcome=\"SUCCESS\"} 15.062229621"], "image": [{"container": "xdr-st-9997725782356-ciem-rule-scanner-dspm", "image": "us-docker.pkg.dev/xdr-registry-dev-01/prismacloud/iam:CIEM-00-check-compliance-scan-log-c491a10267707db217062cd19558d7a6510abd32"}]}, {"scrapeUrl": "http://10.12.9.135:8080/actuator/prometheus", "pod_name": "xdr-st-9997725782356-ciem-rule-scanner-ciem-5664f96f58-w7kht", "app": "xdr-st-9997725782356-ciem-rule-scanner-ciem", "raw_metric": ["ciem_timer_rule_management_filter_rules_seconds_sum{MODULE=\"CIEM\",POLICIES_COUNT=\"3\",RULES_COUNT=\"56\",outcome=\"SUCCESS\"} 9.595343833"], "image": [{"container": "xdr-st-9997725782356-ciem-rule-scanner-ciem", "image": "us-docker.pkg.dev/xdr-registry-dev-01/prismacloud/iam:CIEM-00-check-compliance-scan-log-483be62b647ac4ff0f76a37d006cfbfdddf41113"}]}], "current_ts_count": 3, "cardinality_formula": {"per_pod": "2(MODULE labels) * 2(POLICIES_COUNT labels) * 2(RULES_COUNT labels) * 1(outcome labels) * 2(app labels)", "current": "2(MODULE labels) * 2(POLICIES_COUNT labels) * 2(RULES_COUNT labels) * 1(outcome labels) * 2(app labels) * 2(targets)", "min": "2(MODULE labels) * 2(POLICIES_COUNT labels) * 2(RULES_COUNT labels) * 1(outcome labels) * 2(app labels) * (1(ciem-rule-scanner-dspm pods) + 1(ciem-rule-scanner-ciem pods))", "max": "2(MODULE labels) * 2(POLICIES_COUNT labels) * 2(RULES_COUNT labels) * 1(outcome labels) * 2(app labels) * (1(ciem-rule-scanner-dspm pods) + 1(ciem-rule-scanner-ciem pods))", "avg": "((2(MODULE labels) * 2(POLICIES_COUNT labels) * 2(RULES_COUNT labels) * 1(outcome labels) * 2(app labels) * (1(ciem-rule-scanner-dspm pods) + 1(ciem-rule-scanner-ciem pods))) + (2(MODULE labels) * 2(POLICIES_COUNT labels) * 2(RULES_COUNT labels) * 1(outcome labels) * 2(app labels) * (1(ciem-rule-scanner-dspm pods) + 1(ciem-rule-scanner-ciem pods)))) / 2"}, "cardinality": {"per_pod": 16, "current": 32, "min": 32, "max": 32, "avg": 32.0}}, {"name": "ciem_timer_rule_management_filter_rules_seconds_max", "labels": {"MODULE": ["DSPM", "CIEM"], "POLICIES_COUNT": ["2", "3"], "RULES_COUNT": ["26", "56"], "outcome": ["SUCCESS"], "app": ["xdr-st-9997725782356-ciem-rule-scanner-dspm", "xdr-st-9997725782356-ciem-rule-scanner-ciem"]}, "warnings": ["HPA is not defined for 'ciem-rule-scanner-dspm' (xdr-st-9997725782356-ciem-rule-scanner-dspm)", "HPA is not defined for 'ciem-rule-scanner-ciem' (xdr-st-9997725782356-ciem-rule-scanner-ciem)"], "hpa": {"ciem-rule-scanner-dspm": {"min": 1, "max": 1}, "ciem-rule-scanner-ciem": {"min": 1, "max": 1}}, "targets": [{"scrapeUrl": "http://10.12.9.39:8080/actuator/prometheus", "pod_name": "xdr-st-9997725782356-ciem-rule-scanner-dspm-6c69dd98f4-7zsn8", "app": "xdr-st-9997725782356-ciem-rule-scanner-dspm", "raw_metric": ["# HELP ciem_timer_rule_management_filter_rules_seconds_max  ", "# TYPE ciem_timer_rule_management_filter_rules_seconds_max gauge", "ciem_timer_rule_management_filter_rules_seconds_max{MODULE=\"DSPM\",POLICIES_COUNT=\"2\",RULES_COUNT=\"26\",outcome=\"SUCCESS\"} 0.0", "ciem_timer_rule_management_filter_rules_seconds_max{MODULE=\"DSPM\",POLICIES_COUNT=\"3\",RULES_COUNT=\"26\",outcome=\"SUCCESS\"} 0.0"], "image": [{"container": "xdr-st-9997725782356-ciem-rule-scanner-dspm", "image": "us-docker.pkg.dev/xdr-registry-dev-01/prismacloud/iam:CIEM-00-check-compliance-scan-log-c491a10267707db217062cd19558d7a6510abd32"}]}, {"scrapeUrl": "http://10.12.9.135:8080/actuator/prometheus", "pod_name": "xdr-st-9997725782356-ciem-rule-scanner-ciem-5664f96f58-w7kht", "app": "xdr-st-9997725782356-ciem-rule-scanner-ciem", "raw_metric": ["# HELP ciem_timer_rule_management_filter_rules_seconds_max  ", "# TYPE ciem_timer_rule_management_filter_rules_seconds_max gauge", "ciem_timer_rule_management_filter_rules_seconds_max{MODULE=\"CIEM\",POLICIES_COUNT=\"3\",RULES_COUNT=\"56\",outcome=\"SUCCESS\"} 0.0"], "image": [{"container": "xdr-st-9997725782356-ciem-rule-scanner-ciem", "image": "us-docker.pkg.dev/xdr-registry-dev-01/prismacloud/iam:CIEM-00-check-compliance-scan-log-483be62b647ac4ff0f76a37d006cfbfdddf41113"}]}], "current_ts_count": 3, "cardinality_formula": {"per_pod": "2(MODULE labels) * 2(POLICIES_COUNT labels) * 2(RULES_COUNT labels) * 1(outcome labels) * 2(app labels)", "current": "2(MODULE labels) * 2(POLICIES_COUNT labels) * 2(RULES_COUNT labels) * 1(outcome labels) * 2(app labels) * 2(targets)", "min": "2(MODULE labels) * 2(POLICIES_COUNT labels) * 2(RULES_COUNT labels) * 1(outcome labels) * 2(app labels) * (1(ciem-rule-scanner-dspm pods) + 1(ciem-rule-scanner-ciem pods))", "max": "2(MODULE labels) * 2(POLICIES_COUNT labels) * 2(RULES_COUNT labels) * 1(outcome labels) * 2(app labels) * (1(ciem-rule-scanner-dspm pods) + 1(ciem-rule-scanner-ciem pods))", "avg": "((2(MODULE labels) * 2(POLICIES_COUNT labels) * 2(RULES_COUNT labels) * 1(outcome labels) * 2(app labels) * (1(ciem-rule-scanner-dspm pods) + 1(ciem-rule-scanner-ciem pods))) + (2(MODULE labels) * 2(POLICIES_COUNT labels) * 2(RULES_COUNT labels) * 1(outcome labels) * 2(app labels) * (1(ciem-rule-scanner-dspm pods) + 1(ciem-rule-scanner-ciem pods)))) / 2"}, "cardinality": {"per_pod": 16, "current": 32, "min": 32, "max": 32, "avg": 32.0}}, {"name": "metric_is_not_present", "raw": ["# TARGET: xdr-st-9997725782356-ciem-rule-scanner-dspm-6c69dd98f4-7zsn8, metric_name=ciem_timer_rule_management_filter_rules_seconds_.+", "ciem_timer_rule_management_filter_rules_seconds_max{MODULE=\"DSPM\",POLICIES_COUNT=\"3\",RULES_COUNT=\"26\",outcome=\"SUCCESS\"} 0.0", "# TARGET: xdr-st-9997725782356-ciem-rule-scanner-ciem-5664f96f58-w7kht, metric_name=ciem_timer_rule_management_filter_rules_seconds_.+", "# TYPE ciem_timer_rule_management_filter_rules_seconds_max gauge"]}]