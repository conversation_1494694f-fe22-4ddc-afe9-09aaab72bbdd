#!/usr/bin/env python3
"""
Test script to verify age-based color interpolation
"""

def interpolate_age_color(normalized_age):
    """Interpolate color from green (newest) to red (oldest) via yellow"""
    # Clamp to 0-1 range
    normalized_age = max(0, min(1, normalized_age))

    if normalized_age <= 0.5:
        # Green to Yellow (0.0 to 0.5)
        # Green: #218f3a (33, 143, 58)
        # Yellow: #8f8421 (143, 132, 33)
        t = normalized_age * 2  # Scale to 0-1 for this segment
        r = int(33 + (143 - 33) * t)
        g = int(143 + (132 - 143) * t)
        b = int(58 + (33 - 58) * t)
    else:
        # Yellow to Red (0.5 to 1.0)
        # Yellow: #8f8421 (143, 132, 33)
        # Red: #821d25 (130, 29, 37)
        t = (normalized_age - 0.5) * 2  # Scale to 0-1 for this segment
        r = int(143 + (130 - 143) * t)
        g = int(132 + (29 - 132) * t)
        b = int(33 + (37 - 33) * t)

    return f"#{r:02x}{g:02x}{b:02x}"

def get_contrasting_text_color(background_color):
    """Get contrasting text color (white or black) based on background brightness"""
    # Remove # and convert to RGB
    hex_color = background_color.lstrip('#')
    r = int(hex_color[0:2], 16)
    g = int(hex_color[2:4], 16)
    b = int(hex_color[4:6], 16)
    
    # Calculate brightness using luminance formula
    brightness = (r * 0.299 + g * 0.587 + b * 0.114)
    
    # Return white for dark backgrounds, black for light backgrounds
    return '#ffffff' if brightness < 128 else '#000000'

def main():
    """Test the color interpolation"""
    print("Age-based Color Interpolation Test")
    print("=" * 40)
    print("Age Range: Newest (0.0) → Oldest (1.0)")
    print("Color Range: Green → Yellow → Red")
    print()
    
    # Test various age values
    test_values = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
    
    for age in test_values:
        color = interpolate_age_color(age)
        text_color = get_contrasting_text_color(color)
        age_desc = "Newest" if age == 0.0 else "Oldest" if age == 1.0 else f"Age {age:.1f}"
        
        print(f"{age_desc:>10}: Background {color}, Text {text_color}")
    
    print()
    print("Key colors:")
    print(f"  Green (newest): {interpolate_age_color(0.0)}")
    print(f"  Yellow (mid):   {interpolate_age_color(0.5)}")
    print(f"  Red (oldest):   {interpolate_age_color(1.0)}")
    print()
    print("Expected colors:")
    print("  Green (newest): #218f3a")
    print("  Yellow (mid):   #8f8421")
    print("  Red (oldest):   #821d25")

if __name__ == "__main__":
    main()
