-- Logs begin at Sun 2025-09-14 08:29:18 UTC. --
Sep 15 23:27:34 prometheus-server prometheus[1126682]: time=2025-09-15T23:27:34.434Z level=WARN source=group.go:544 msg="Evaluating rule failed" component="rule manager" file=/etc/prometheus/rules/prod/tenant.yml group=tenant.rules name=ST-APP-Pods-CrashLoop index=50 rule="alert: ST-APP-Pods-CrashLoop\nexpr: kube:pod_restart:sum1h{container!~\".*verdict-manager.*|.*fetcher.*|.*ingester.*|.*matchingservice.*|.*analytics.*|.*xql.*|.*dms.*|.*lavawall.*|.*pipeline.*|.*pz-schema-manager.*|.*storybuilder.*|.*metrics-aggregator.*\",job=~\"tenant-metrics.*\",namespace!~\"octopus|observability.*\",pod!~\".*(elastic-es-xsoar-exporter).*\",pod!~\".*(vsg-controller-manager|.*ipl-cronus-operator-controller-manager|scylla-operator|dp-singlestore-operator|detection-rules).*\",pod!~\".*-[0-9]*-(analytics-alerts-emitter|analytics-decider|analytics-detection-engine).*\",pod!~\".*-[0-9]*-(analytics-profiles-orchestrator).*\",pod!~\".*-[0-9]*-(api-xsoar|xsoar-api|engine-hub|pb-runner-v2|xsoar|xsoar-content|xsoar-init|xsoar-migration|xsoar-redis|xsoar-workers-gateway|xsoar-workers-router).*\",pod!~\".*-[0-9]*-(apisec-grouping-service|apisec-enricher-service|apisec-asset-manager|apisec-risk-engine|apisec-inspection-service|risk-score-cie-processor).*\",pod!~\".*-[0-9]*-(apisec-spec-service|apisec-spec|apisec-spec-gate|apisec-scan-manager-service|apisec-bff-service|apisec-asset-mgr).*\",pod!~\".*-[0-9]*-(cold-storage-datasets-aggregator-worker|dms|dp-finding-pipeline|dp-uai-findings|dp-scan-logs-pipeline|dp-analytics-scan-logs|egress-forwarding|ipl-asset-engine|dp-uai-assets-legacy|metrics-aggregator|pipeline|xcloud-ingester|xql-cdl-engine|xql-engine|xql-fdr-engine).*\",pod!~\".*-[0-9]*-(dp-asset-ingester|dp-uai-assets|ipl-asset-redis|pz-schema-manager|xcloud-redis).*\",pod!~\".*-[0-9]*-(itdr-asset-retention|itdr-data-pipeline|itdr-risk-processor-service|itdr-api).*\",pod!~\".*-[0-9]*-(itdr-bigquery-migrator).*\",pod!~\".*-[0-9]*-ciem.*\",pod!~\".*-[0-9]*-cwp.*\",pod!~\".*-[0-9]*-dp-asset-association-pipeline.*\",pod!~\".*-[0-9]*-dp-uai-assets-association.*\",pod!~\".*-[0-9]*-dspm.*\",pod!~\".*-[0-9]*-email-artifacts-relay.*\",pod!~\".*-[0-9]*-uvem-vxp-api.*\",pod!~\".*dspm-ai-data-connector.*\",tenant_type!=\"internal\"}\n  > 10 unless on (lcaas_id) (xsoar_migration_status{tenant_type!=\"internal\"} > 0)\n  unless on (lcaas_id) (xdr_init_app_status{tenant_type!=\"internal\"} > 0)\nfor: 3h\nlabels:\n  pd_service: xdr-st-service\n  severity: warning\nannotations:\n  description: 'Check pod logs first, by running: kubectl -n {{ $labels.namespace\n    }} logs {{ $labels.pod }}, then check The confluence in the runbook annotation\n    for known errors and their fixes.'\n  runbook: \"Check for possible solution here - https://confluence-dc.paloaltonetworks.com/display/VisibilityApplication/Runbook+-+CrashLoop+and+DeadPods+Alerts\n    \\nCheck owners of the components here - https://confluence-dc.paloaltonetworks.com/display/VisibilityApplication/Incident+Runbooks\\n\"\n  title: 'PROBLEM: Project {{ $labels.tenant_id }} container {{ $labels.container\n    }} pod {{ $labels.pod }} in namespace {{ $labels.namespace }} has restarted over\n    {{ $value }} times for the past 1 hour'\n" err="query timed out in expression evaluation"
Sep 15 23:32:53 prometheus-server prometheus[1126682]: time=2025-09-15T23:32:53.721Z level=WARN source=group.go:544 msg="Evaluating rule failed" component="rule manager" file=/etc/prometheus/rules/prod/tenant.yml group=tenant.rules name=ST-APP-Pods-CrashLoop index=50 rule="alert: ST-APP-Pods-CrashLoop\nexpr: kube:pod_restart:sum1h{container!~\".*verdict-manager.*|.*fetcher.*|.*ingester.*|.*matchingservice.*|.*analytics.*|.*xql.*|.*dms.*|.*lavawall.*|.*pipeline.*|.*pz-schema-manager.*|.*storybuilder.*|.*metrics-aggregator.*\",job=~\"tenant-metrics.*\",namespace!~\"octopus|observability.*\",pod!~\".*(elastic-es-xsoar-exporter).*\",pod!~\".*(vsg-controller-manager|.*ipl-cronus-operator-controller-manager|scylla-operator|dp-singlestore-operator|detection-rules).*\",pod!~\".*-[0-9]*-(analytics-alerts-emitter|analytics-decider|analytics-detection-engine).*\",pod!~\".*-[0-9]*-(analytics-profiles-orchestrator).*\",pod!~\".*-[0-9]*-(api-xsoar|xsoar-api|engine-hub|pb-runner-v2|xsoar|xsoar-content|xsoar-init|xsoar-migration|xsoar-redis|xsoar-workers-gateway|xsoar-workers-router).*\",pod!~\".*-[0-9]*-(apisec-grouping-service|apisec-enricher-service|apisec-asset-manager|apisec-risk-engine|apisec-inspection-service|risk-score-cie-processor).*\",pod!~\".*-[0-9]*-(apisec-spec-service|apisec-spec|apisec-spec-gate|apisec-scan-manager-service|apisec-bff-service|apisec-asset-mgr).*\",pod!~\".*-[0-9]*-(cold-storage-datasets-aggregator-worker|dms|dp-finding-pipeline|dp-uai-findings|dp-scan-logs-pipeline|dp-analytics-scan-logs|egress-forwarding|ipl-asset-engine|dp-uai-assets-legacy|metrics-aggregator|pipeline|xcloud-ingester|xql-cdl-engine|xql-engine|xql-fdr-engine).*\",pod!~\".*-[0-9]*-(dp-asset-ingester|dp-uai-assets|ipl-asset-redis|pz-schema-manager|xcloud-redis).*\",pod!~\".*-[0-9]*-(itdr-asset-retention|itdr-data-pipeline|itdr-risk-processor-service|itdr-api).*\",pod!~\".*-[0-9]*-(itdr-bigquery-migrator).*\",pod!~\".*-[0-9]*-ciem.*\",pod!~\".*-[0-9]*-cwp.*\",pod!~\".*-[0-9]*-dp-asset-association-pipeline.*\",pod!~\".*-[0-9]*-dp-uai-assets-association.*\",pod!~\".*-[0-9]*-dspm.*\",pod!~\".*-[0-9]*-email-artifacts-relay.*\",pod!~\".*-[0-9]*-uvem-vxp-api.*\",pod!~\".*dspm-ai-data-connector.*\",tenant_type!=\"internal\"}\n  > 10 unless on (lcaas_id) (xsoar_migration_status{tenant_type!=\"internal\"} > 0)\n  unless on (lcaas_id) (xdr_init_app_status{tenant_type!=\"internal\"} > 0)\nfor: 3h\nlabels:\n  pd_service: xdr-st-service\n  severity: warning\nannotations:\n  description: 'Check pod logs first, by running: kubectl -n {{ $labels.namespace\n    }} logs {{ $labels.pod }}, then check The confluence in the runbook annotation\n    for known errors and their fixes.'\n  runbook: \"Check for possible solution here - https://confluence-dc.paloaltonetworks.com/display/VisibilityApplication/Runbook+-+CrashLoop+and+DeadPods+Alerts\n    \\nCheck owners of the components here - https://confluence-dc.paloaltonetworks.com/display/VisibilityApplication/Incident+Runbooks\\n\"\n  title: 'PROBLEM: Project {{ $labels.tenant_id }} container {{ $labels.container\n    }} pod {{ $labels.pod }} in namespace {{ $labels.namespace }} has restarted over\n    {{ $value }} times for the past 1 hour'\n" err="query timed out in expression evaluation"
Sep 15 23:38:39 prometheus-server prometheus[1126682]: time=2025-09-15T23:38:39.380Z level=WARN source=group.go:544 msg="Evaluating rule failed" component="rule manager" file=/etc/prometheus/rules/prod/tenant.yml group=tenant.rules name=ST-APP-Pods-CrashLoop index=50 rule="alert: ST-APP-Pods-CrashLoop\nexpr: kube:pod_restart:sum1h{container!~\".*verdict-manager.*|.*fetcher.*|.*ingester.*|.*matchingservice.*|.*analytics.*|.*xql.*|.*dms.*|.*lavawall.*|.*pipeline.*|.*pz-schema-manager.*|.*storybuilder.*|.*metrics-aggregator.*\",job=~\"tenant-metrics.*\",namespace!~\"octopus|observability.*\",pod!~\".*(elastic-es-xsoar-exporter).*\",pod!~\".*(vsg-controller-manager|.*ipl-cronus-operator-controller-manager|scylla-operator|dp-singlestore-operator|detection-rules).*\",pod!~\".*-[0-9]*-(analytics-alerts-emitter|analytics-decider|analytics-detection-engine).*\",pod!~\".*-[0-9]*-(analytics-profiles-orchestrator).*\",pod!~\".*-[0-9]*-(api-xsoar|xsoar-api|engine-hub|pb-runner-v2|xsoar|xsoar-content|xsoar-init|xsoar-migration|xsoar-redis|xsoar-workers-gateway|xsoar-workers-router).*\",pod!~\".*-[0-9]*-(apisec-grouping-service|apisec-enricher-service|apisec-asset-manager|apisec-risk-engine|apisec-inspection-service|risk-score-cie-processor).*\",pod!~\".*-[0-9]*-(apisec-spec-service|apisec-spec|apisec-spec-gate|apisec-scan-manager-service|apisec-bff-service|apisec-asset-mgr).*\",pod!~\".*-[0-9]*-(cold-storage-datasets-aggregator-worker|dms|dp-finding-pipeline|dp-uai-findings|dp-scan-logs-pipeline|dp-analytics-scan-logs|egress-forwarding|ipl-asset-engine|dp-uai-assets-legacy|metrics-aggregator|pipeline|xcloud-ingester|xql-cdl-engine|xql-engine|xql-fdr-engine).*\",pod!~\".*-[0-9]*-(dp-asset-ingester|dp-uai-assets|ipl-asset-redis|pz-schema-manager|xcloud-redis).*\",pod!~\".*-[0-9]*-(itdr-asset-retention|itdr-data-pipeline|itdr-risk-processor-service|itdr-api).*\",pod!~\".*-[0-9]*-(itdr-bigquery-migrator).*\",pod!~\".*-[0-9]*-ciem.*\",pod!~\".*-[0-9]*-cwp.*\",pod!~\".*-[0-9]*-dp-asset-association-pipeline.*\",pod!~\".*-[0-9]*-dp-uai-assets-association.*\",pod!~\".*-[0-9]*-dspm.*\",pod!~\".*-[0-9]*-email-artifacts-relay.*\",pod!~\".*-[0-9]*-uvem-vxp-api.*\",pod!~\".*dspm-ai-data-connector.*\",tenant_type!=\"internal\"}\n  > 10 unless on (lcaas_id) (xsoar_migration_status{tenant_type!=\"internal\"} > 0)\n  unless on (lcaas_id) (xdr_init_app_status{tenant_type!=\"internal\"} > 0)\nfor: 3h\nlabels:\n  pd_service: xdr-st-service\n  severity: warning\nannotations:\n  description: 'Check pod logs first, by running: kubectl -n {{ $labels.namespace\n    }} logs {{ $labels.pod }}, then check The confluence in the runbook annotation\n    for known errors and their fixes.'\n  runbook: \"Check for possible solution here - https://confluence-dc.paloaltonetworks.com/display/VisibilityApplication/Runbook+-+CrashLoop+and+DeadPods+Alerts\n    \\nCheck owners of the components here - https://confluence-dc.paloaltonetworks.com/display/VisibilityApplication/Incident+Runbooks\\n\"\n  title: 'PROBLEM: Project {{ $labels.tenant_id }} container {{ $labels.container\n    }} pod {{ $labels.pod }} in namespace {{ $labels.namespace }} has restarted over\n    {{ $value }} times for the past 1 hour'\n" err="query timed out in expression evaluation"
Sep 15 23:41:39 prometheus-server prometheus[1126682]: time=2025-09-15T23:41:39.116Z level=INFO source=head.go:1410 msg="Head GC completed" component=tsdb caller=truncateMemory duration=19m36.721640719s
Sep 16 00:49:51 prometheus-server prometheus[1126682]: time=2025-09-16T00:49:51.101Z level=INFO source=compact.go:606 msg="write block" component=tsdb mint=1757966400000 maxt=1757973600000 ulid=01K57W2SXD2NMVYMCTZK041VJY duration=1h8m11.983651739s ooo=false
Sep 16 00:49:55 prometheus-server prometheus[1126682]: time=2025-09-16T00:49:55.306Z level=INFO source=db.go:1814 msg="Deleting obsolete block" component=tsdb block=01K4NS0GY97CTMVJ12DJ22WDX9
Sep 16 00:57:45 prometheus-server prometheus[1126682]: time=2025-09-16T00:57:45.458Z level=WARN source=group.go:544 msg="Evaluating rule failed" component="rule manager" file=/etc/prometheus/rules/prod/tenant.yml group=tenant.rules name=ST-APP-Pods-CrashLoop index=50 rule="alert: ST-APP-Pods-CrashLoop\nexpr: kube:pod_restart:sum1h{container!~\".*verdict-manager.*|.*fetcher.*|.*ingester.*|.*matchingservice.*|.*analytics.*|.*xql.*|.*dms.*|.*lavawall.*|.*pipeline.*|.*pz-schema-manager.*|.*storybuilder.*|.*metrics-aggregator.*\",job=~\"tenant-metrics.*\",namespace!~\"octopus|observability.*\",pod!~\".*(elastic-es-xsoar-exporter).*\",pod!~\".*(vsg-controller-manager|.*ipl-cronus-operator-controller-manager|scylla-operator|dp-singlestore-operator|detection-rules).*\",pod!~\".*-[0-9]*-(analytics-alerts-emitter|analytics-decider|analytics-detection-engine).*\",pod!~\".*-[0-9]*-(analytics-profiles-orchestrator).*\",pod!~\".*-[0-9]*-(api-xsoar|xsoar-api|engine-hub|pb-runner-v2|xsoar|xsoar-content|xsoar-init|xsoar-migration|xsoar-redis|xsoar-workers-gateway|xsoar-workers-router).*\",pod!~\".*-[0-9]*-(apisec-grouping-service|apisec-enricher-service|apisec-asset-manager|apisec-risk-engine|apisec-inspection-service|risk-score-cie-processor).*\",pod!~\".*-[0-9]*-(apisec-spec-service|apisec-spec|apisec-spec-gate|apisec-scan-manager-service|apisec-bff-service|apisec-asset-mgr).*\",pod!~\".*-[0-9]*-(cold-storage-datasets-aggregator-worker|dms|dp-finding-pipeline|dp-uai-findings|dp-scan-logs-pipeline|dp-analytics-scan-logs|egress-forwarding|ipl-asset-engine|dp-uai-assets-legacy|metrics-aggregator|pipeline|xcloud-ingester|xql-cdl-engine|xql-engine|xql-fdr-engine).*\",pod!~\".*-[0-9]*-(dp-asset-ingester|dp-uai-assets|ipl-asset-redis|pz-schema-manager|xcloud-redis).*\",pod!~\".*-[0-9]*-(itdr-asset-retention|itdr-data-pipeline|itdr-risk-processor-service|itdr-api).*\",pod!~\".*-[0-9]*-(itdr-bigquery-migrator).*\",pod!~\".*-[0-9]*-ciem.*\",pod!~\".*-[0-9]*-cwp.*\",pod!~\".*-[0-9]*-dp-asset-association-pipeline.*\",pod!~\".*-[0-9]*-dp-uai-assets-association.*\",pod!~\".*-[0-9]*-dspm.*\",pod!~\".*-[0-9]*-email-artifacts-relay.*\",pod!~\".*-[0-9]*-uvem-vxp-api.*\",pod!~\".*dspm-ai-data-connector.*\",tenant_type!=\"internal\"}\n  > 10 unless on (lcaas_id) (xsoar_migration_status{tenant_type!=\"internal\"} > 0)\n  unless on (lcaas_id) (xdr_init_app_status{tenant_type!=\"internal\"} > 0)\nfor: 3h\nlabels:\n  pd_service: xdr-st-service\n  severity: warning\nannotations:\n  description: 'Check pod logs first, by running: kubectl -n {{ $labels.namespace\n    }} logs {{ $labels.pod }}, then check The confluence in the runbook annotation\n    for known errors and their fixes.'\n  runbook: \"Check for possible solution here - https://confluence-dc.paloaltonetworks.com/display/VisibilityApplication/Runbook+-+CrashLoop+and+DeadPods+Alerts\n    \\nCheck owners of the components here - https://confluence-dc.paloaltonetworks.com/display/VisibilityApplication/Incident+Runbooks\\n\"\n  title: 'PROBLEM: Project {{ $labels.tenant_id }} container {{ $labels.container\n    }} pod {{ $labels.pod }} in namespace {{ $labels.namespace }} has restarted over\n    {{ $value }} times for the past 1 hour'\n" err="expanding series: context deadline exceeded"
Sep 16 01:02:38 prometheus-server prometheus[1126682]: time=2025-09-16T01:02:38.946Z level=WARN source=group.go:544 msg="Evaluating rule failed" component="rule manager" file=/etc/prometheus/rules/prod/tenant.yml group=tenant.rules name=ST-APP-Pods-CrashLoop index=50 rule="alert: ST-APP-Pods-CrashLoop\nexpr: kube:pod_restart:sum1h{container!~\".*verdict-manager.*|.*fetcher.*|.*ingester.*|.*matchingservice.*|.*analytics.*|.*xql.*|.*dms.*|.*lavawall.*|.*pipeline.*|.*pz-schema-manager.*|.*storybuilder.*|.*metrics-aggregator.*\",job=~\"tenant-metrics.*\",namespace!~\"octopus|observability.*\",pod!~\".*(elastic-es-xsoar-exporter).*\",pod!~\".*(vsg-controller-manager|.*ipl-cronus-operator-controller-manager|scylla-operator|dp-singlestore-operator|detection-rules).*\",pod!~\".*-[0-9]*-(analytics-alerts-emitter|analytics-decider|analytics-detection-engine).*\",pod!~\".*-[0-9]*-(analytics-profiles-orchestrator).*\",pod!~\".*-[0-9]*-(api-xsoar|xsoar-api|engine-hub|pb-runner-v2|xsoar|xsoar-content|xsoar-init|xsoar-migration|xsoar-redis|xsoar-workers-gateway|xsoar-workers-router).*\",pod!~\".*-[0-9]*-(apisec-grouping-service|apisec-enricher-service|apisec-asset-manager|apisec-risk-engine|apisec-inspection-service|risk-score-cie-processor).*\",pod!~\".*-[0-9]*-(apisec-spec-service|apisec-spec|apisec-spec-gate|apisec-scan-manager-service|apisec-bff-service|apisec-asset-mgr).*\",pod!~\".*-[0-9]*-(cold-storage-datasets-aggregator-worker|dms|dp-finding-pipeline|dp-uai-findings|dp-scan-logs-pipeline|dp-analytics-scan-logs|egress-forwarding|ipl-asset-engine|dp-uai-assets-legacy|metrics-aggregator|pipeline|xcloud-ingester|xql-cdl-engine|xql-engine|xql-fdr-engine).*\",pod!~\".*-[0-9]*-(dp-asset-ingester|dp-uai-assets|ipl-asset-redis|pz-schema-manager|xcloud-redis).*\",pod!~\".*-[0-9]*-(itdr-asset-retention|itdr-data-pipeline|itdr-risk-processor-service|itdr-api).*\",pod!~\".*-[0-9]*-(itdr-bigquery-migrator).*\",pod!~\".*-[0-9]*-ciem.*\",pod!~\".*-[0-9]*-cwp.*\",pod!~\".*-[0-9]*-dp-asset-association-pipeline.*\",pod!~\".*-[0-9]*-dp-uai-assets-association.*\",pod!~\".*-[0-9]*-dspm.*\",pod!~\".*-[0-9]*-email-artifacts-relay.*\",pod!~\".*-[0-9]*-uvem-vxp-api.*\",pod!~\".*dspm-ai-data-connector.*\",tenant_type!=\"internal\"}\n  > 10 unless on (lcaas_id) (xsoar_migration_status{tenant_type!=\"internal\"} > 0)\n  unless on (lcaas_id) (xdr_init_app_status{tenant_type!=\"internal\"} > 0)\nfor: 3h\nlabels:\n  pd_service: xdr-st-service\n  severity: warning\nannotations:\n  description: 'Check pod logs first, by running: kubectl -n {{ $labels.namespace\n    }} logs {{ $labels.pod }}, then check The confluence in the runbook annotation\n    for known errors and their fixes.'\n  runbook: \"Check for possible solution here - https://confluence-dc.paloaltonetworks.com/display/VisibilityApplication/Runbook+-+CrashLoop+and+DeadPods+Alerts\n    \\nCheck owners of the components here - https://confluence-dc.paloaltonetworks.com/display/VisibilityApplication/Incident+Runbooks\\n\"\n  title: 'PROBLEM: Project {{ $labels.tenant_id }} container {{ $labels.container\n    }} pod {{ $labels.pod }} in namespace {{ $labels.namespace }} has restarted over\n    {{ $value }} times for the past 1 hour'\n" err="query timed out in expression evaluation"
Sep 16 01:07:52 prometheus-server prometheus[1126682]: time=2025-09-16T01:07:52.027Z level=WARN source=group.go:544 msg="Evaluating rule failed" component="rule manager" file=/etc/prometheus/rules/prod/tenant.yml group=tenant.rules name=ST-APP-Pods-CrashLoop index=50 rule="alert: ST-APP-Pods-CrashLoop\nexpr: kube:pod_restart:sum1h{container!~\".*verdict-manager.*|.*fetcher.*|.*ingester.*|.*matchingservice.*|.*analytics.*|.*xql.*|.*dms.*|.*lavawall.*|.*pipeline.*|.*pz-schema-manager.*|.*storybuilder.*|.*metrics-aggregator.*\",job=~\"tenant-metrics.*\",namespace!~\"octopus|observability.*\",pod!~\".*(elastic-es-xsoar-exporter).*\",pod!~\".*(vsg-controller-manager|.*ipl-cronus-operator-controller-manager|scylla-operator|dp-singlestore-operator|detection-rules).*\",pod!~\".*-[0-9]*-(analytics-alerts-emitter|analytics-decider|analytics-detection-engine).*\",pod!~\".*-[0-9]*-(analytics-profiles-orchestrator).*\",pod!~\".*-[0-9]*-(api-xsoar|xsoar-api|engine-hub|pb-runner-v2|xsoar|xsoar-content|xsoar-init|xsoar-migration|xsoar-redis|xsoar-workers-gateway|xsoar-workers-router).*\",pod!~\".*-[0-9]*-(apisec-grouping-service|apisec-enricher-service|apisec-asset-manager|apisec-risk-engine|apisec-inspection-service|risk-score-cie-processor).*\",pod!~\".*-[0-9]*-(apisec-spec-service|apisec-spec|apisec-spec-gate|apisec-scan-manager-service|apisec-bff-service|apisec-asset-mgr).*\",pod!~\".*-[0-9]*-(cold-storage-datasets-aggregator-worker|dms|dp-finding-pipeline|dp-uai-findings|dp-scan-logs-pipeline|dp-analytics-scan-logs|egress-forwarding|ipl-asset-engine|dp-uai-assets-legacy|metrics-aggregator|pipeline|xcloud-ingester|xql-cdl-engine|xql-engine|xql-fdr-engine).*\",pod!~\".*-[0-9]*-(dp-asset-ingester|dp-uai-assets|ipl-asset-redis|pz-schema-manager|xcloud-redis).*\",pod!~\".*-[0-9]*-(itdr-asset-retention|itdr-data-pipeline|itdr-risk-processor-service|itdr-api).*\",pod!~\".*-[0-9]*-(itdr-bigquery-migrator).*\",pod!~\".*-[0-9]*-ciem.*\",pod!~\".*-[0-9]*-cwp.*\",pod!~\".*-[0-9]*-dp-asset-association-pipeline.*\",pod!~\".*-[0-9]*-dp-uai-assets-association.*\",pod!~\".*-[0-9]*-dspm.*\",pod!~\".*-[0-9]*-email-artifacts-relay.*\",pod!~\".*-[0-9]*-uvem-vxp-api.*\",pod!~\".*dspm-ai-data-connector.*\",tenant_type!=\"internal\"}\n  > 10 unless on (lcaas_id) (xsoar_migration_status{tenant_type!=\"internal\"} > 0)\n  unless on (lcaas_id) (xdr_init_app_status{tenant_type!=\"internal\"} > 0)\nfor: 3h\nlabels:\n  pd_service: xdr-st-service\n  severity: warning\nannotations:\n  description: 'Check pod logs first, by running: kubectl -n {{ $labels.namespace\n    }} logs {{ $labels.pod }}, then check The confluence in the runbook annotation\n    for known errors and their fixes.'\n  runbook: \"Check for possible solution here - https://confluence-dc.paloaltonetworks.com/display/VisibilityApplication/Runbook+-+CrashLoop+and+DeadPods+Alerts\n    \\nCheck owners of the components here - https://confluence-dc.paloaltonetworks.com/display/VisibilityApplication/Incident+Runbooks\\n\"\n  title: 'PROBLEM: Project {{ $labels.tenant_id }} container {{ $labels.container\n    }} pod {{ $labels.pod }} in namespace {{ $labels.namespace }} has restarted over\n    {{ $value }} times for the past 1 hour'\n" err="query timed out in expression evaluation"
Sep 16 01:10:39 prometheus-server prometheus[1126682]: time=2025-09-16T01:10:39.259Z level=INFO source=head.go:1410 msg="Head GC completed" component=tsdb caller=truncateMemory duration=20m43.953152338s