import os
import ssl
from datetime import datetime, timedelta
from slack_sdk import <PERSON><PERSON><PERSON>
from slack_sdk.errors import SlackApiError

# --- Configuration ---
# It's best practice to set this as an environment variable
# export SLACK_BOT_TOKEN="xoxb-your-token-here"
lcaas_id = "9997464373539"
SLACK_TOKEN = os.environ.get("SLACK_BOT_TOKEN")
SEARCH_STRING = "playbook"
SEARCH_NUMBER = f"qa2-test-{lcaas_id}"
DAYS_AGO = 1


ssl_context = ssl.create_default_context()
ssl_context.check_hostname = False
ssl_context.verify_mode = ssl.CERT_NONE
# --- Initialize Slack Client ---
client = WebClient(token=SLACK_TOKEN, ssl=ssl_context)

def get_message_threads(search_query: str):
    """
    Finds all messages matching a query and then fetches their full threads.
    """
    unique_threads = {} # Using a dict to store {thread_ts: channel_id} to avoid duplicates

    try:
        # Step 1: Search for all messages matching the query
        print(f"🔍 Searching for messages with query: '{search_query}'...")
        result = client.search_messages(query=search_query, sort="timestamp", sort_dir="desc")

        if not result["messages"]["matches"]:
            print("No matching messages found.")
            return {}

        # Step 2: Collect unique thread identifiers from the search results
        for message in result["messages"]["matches"]:
            # A message is part of a thread if 'thread_ts' exists.
            # 'thread_ts' is the timestamp of the PARENT message.
            if message.get("thread_ts"):
                thread_ts = message["thread_ts"]
                channel_id = message["channel"]["id"]
                # Store the channel ID with the thread_ts to fetch it later
                if thread_ts not in unique_threads:
                    unique_threads[thread_ts] = channel_id
        
        if not unique_threads:
            print("Found matching messages, but none were in a thread.")
            return {}

        print(f"Found {len(unique_threads)} unique threads. Fetching them now...")

        # Step 3: Fetch the full content of each unique thread
        all_threads_content = {}
        for ts, channel in unique_threads.items():
            thread_replies = client.conversations_replies(channel=channel, ts=ts)
            all_threads_content[ts] = thread_replies["messages"]
        
        return all_threads_content

    except SlackApiError as e:
        print(f"Error: {e.response['error']}")
        return {}

if __name__ == "__main__":
    # Calculate the 'after' date for the search query
    start_date = datetime.now() - timedelta(days=DAYS_AGO)
    date_filter = start_date.strftime("%Y-%m-%d")

    # Construct the final search query
    # This query finds messages containing BOTH the string AND the number, after the specified date.
    query = f'"{SEARCH_STRING}" {SEARCH_NUMBER} after:{date_filter}'

    # Run the main function
    threads = get_message_threads(query)

    # Print the results
    if threads:
        print("\n--- Fetched Threads ---")
        for thread_ts, messages in threads.items():
            print(f"\n🧵 Thread ID (timestamp): {thread_ts}")
            for msg in messages:
                user = msg.get('user', 'Unknown User')
                text = msg.get('text', '')
                print(f"  > [{user}]: {text}")
        print("\n✅ Done.")