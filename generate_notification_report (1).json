[{"name": "xdr_generate_notification_error_total", "labels": {"app": ["xdr-st-9993251414589-frontend", "xdr-st-9993251414589-api", "xdr-st-9993251414589-metrics"]}, "warnings": ["Number of targets for xdr_generate_notification_error_total is high: 4"], "hpa": {"frontend": {"min": 1, "max": 100}, "api": {"min": 1, "max": 200}, "metrics": {"min": 1, "max": 3}}, "targets": [{"scrapeUrl": "http://10.12.7.12:6543/metrics", "pod_name": "xdr-st-9993251414589-frontend-74cfcbb46f-xwvwf", "app": "xdr-st-9993251414589-frontend", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/frontend:xsiam--dev-platform-v4.3.0-4824171-gffdadac0", "container": "nginx"}, {"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/backend:CRTX-197032_empty_connector_name_bug_fix-stable-platform-v4.3.0-5988-g946fd4-27c3", "container": "pyramid"}], "raw_metric": ["# HELP xdr_generate_notification_error_total Got error from On Demand notifications handler", "# TYPE xdr_generate_notification_error_total counter", "xdr_generate_notification_error_total 0.0"]}, {"scrapeUrl": "http://10.12.4.172:4999/metrics", "pod_name": "xdr-st-9993251414589-api-75df774c4-l8r47", "app": "xdr-st-9993251414589-api", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/backend:CRTX-185416_emitting_metrics-stable-platform-v4.3.0-6060-gc9befa-ca9e", "container": "xdr-st-9993251414589-api"}], "raw_metric": ["# HELP xdr_generate_notification_error_total Got error from On Demand notifications handler", "# TYPE xdr_generate_notification_error_total counter", "xdr_generate_notification_error_total 0.0"]}, {"scrapeUrl": "http://10.12.2.182:4999/metrics", "pod_name": "xdr-st-9993251414589-api-75df774c4-tkbtb", "app": "xdr-st-9993251414589-api", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/backend:CRTX-185416_emitting_metrics-stable-platform-v4.3.0-6060-gc9befa-ca9e", "container": "xdr-st-9993251414589-api"}], "raw_metric": ["# HELP xdr_generate_notification_error_total Got error from On Demand notifications handler", "# TYPE xdr_generate_notification_error_total counter", "xdr_generate_notification_error_total 0.0"]}, {"scrapeUrl": "http://10.12.8.33:6669/metrics", "pod_name": "xdr-st-9993251414589-metrics-77bcc5c7c6-ztscj", "app": "xdr-st-9993251414589-metrics", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/backend:CRTX-197032_empty_connector_name_bug_fix-stable-platform-v4.3.0-5988-g946fd4-27c3", "container": "xdr-st-9993251414589-metrics"}], "raw_metric": ["# HELP xdr_generate_notification_error_total Got error from On Demand notifications handler", "# TYPE xdr_generate_notification_error_total counter", "xdr_generate_notification_error_total 0.0"]}], "cardinality_formula": {"per_pod": "3(app labels)", "current": "3(app labels) * 4(targets)", "min": "3(app labels) * (1(frontend pods) + 1(api pods) + 1(metrics pods))", "max": "3(app labels) * (100(frontend pods) + 200(api pods) + 3(metrics pods))", "avg": "((3(app labels) * (1(frontend pods) + 1(api pods) + 1(metrics pods))) + (3(app labels) * (100(frontend pods) + 200(api pods) + 3(metrics pods)))) / 2"}, "cardinality": {"per_pod": 3, "current": 12, "min": 9, "max": 909, "avg": 459.0}}, {"name": "xdr_generate_notification_error_created", "labels": {"app": ["xdr-st-9993251414589-frontend", "xdr-st-9993251414589-api", "xdr-st-9993251414589-metrics"]}, "warnings": ["Number of targets for xdr_generate_notification_error_created is high: 4"], "hpa": {"frontend": {"min": 1, "max": 100}, "api": {"min": 1, "max": 200}, "metrics": {"min": 1, "max": 3}}, "targets": [{"scrapeUrl": "http://10.12.7.12:6543/metrics", "pod_name": "xdr-st-9993251414589-frontend-74cfcbb46f-xwvwf", "app": "xdr-st-9993251414589-frontend", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/frontend:xsiam--dev-platform-v4.3.0-4824171-gffdadac0", "container": "nginx"}, {"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/backend:CRTX-197032_empty_connector_name_bug_fix-stable-platform-v4.3.0-5988-g946fd4-27c3", "container": "pyramid"}], "raw_metric": ["# TYPE xdr_generate_notification_error_created gauge", "xdr_generate_notification_error_created 1758087920.6830158"]}, {"scrapeUrl": "http://10.12.4.172:4999/metrics", "pod_name": "xdr-st-9993251414589-api-75df774c4-l8r47", "app": "xdr-st-9993251414589-api", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/backend:CRTX-185416_emitting_metrics-stable-platform-v4.3.0-6060-gc9befa-ca9e", "container": "xdr-st-9993251414589-api"}], "raw_metric": ["# TYPE xdr_generate_notification_error_created gauge", "xdr_generate_notification_error_created 1758105101.4329107"]}, {"scrapeUrl": "http://10.12.2.182:4999/metrics", "pod_name": "xdr-st-9993251414589-api-75df774c4-tkbtb", "app": "xdr-st-9993251414589-api", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/backend:CRTX-185416_emitting_metrics-stable-platform-v4.3.0-6060-gc9befa-ca9e", "container": "xdr-st-9993251414589-api"}], "raw_metric": ["# TYPE xdr_generate_notification_error_created gauge", "xdr_generate_notification_error_created 1758117881.3547516"]}, {"scrapeUrl": "http://10.12.8.33:6669/metrics", "pod_name": "xdr-st-9993251414589-metrics-77bcc5c7c6-ztscj", "app": "xdr-st-9993251414589-metrics", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/backend:CRTX-197032_empty_connector_name_bug_fix-stable-platform-v4.3.0-5988-g946fd4-27c3", "container": "xdr-st-9993251414589-metrics"}], "raw_metric": ["# TYPE xdr_generate_notification_error_created gauge", "xdr_generate_notification_error_created 1758088853.5842514"]}], "cardinality_formula": {"per_pod": "3(app labels)", "current": "3(app labels) * 4(targets)", "min": "3(app labels) * (1(frontend pods) + 1(api pods) + 1(metrics pods))", "max": "3(app labels) * (100(frontend pods) + 200(api pods) + 3(metrics pods))", "avg": "((3(app labels) * (1(frontend pods) + 1(api pods) + 1(metrics pods))) + (3(app labels) * (100(frontend pods) + 200(api pods) + 3(metrics pods)))) / 2"}, "cardinality": {"per_pod": 3, "current": 12, "min": 9, "max": 909, "avg": 459.0}}]