[{"name": "cloud_assets_collection_csp_api_request_duration_seconds_bucket", "labels": {"action": ["Get"], "api_status_code": ["200"], "app": ["xdr-st-*************-ca-collection-fsi-workers"], "app_name": ["xdr-st-*************-ca-collection-fsi-workers"], "cloud_provider": ["AWS"], "le": ["+Inf", "0.005", "0.01", "0.025", "0.05", "0.1", "0.25", "0.5", "1.0", "10.0", "2.5", "5.0", "1", "5", "10"], "owner_panw_group": ["platform"], "owner_panw_people_slack_handle_owners_group": ["m<PERSON><PERSON><PERSON>"], "owner_panw_people_slack_handle_team_lead": ["<PERSON><PERSON><PERSON>"], "owner_panw_team": ["asset"], "owner_panw_team_slack_handle": ["platform-collection-owners"], "rate_limit_group": ["shared-gubernator"], "resource": ["Token"], "service": ["api.ecr-public"], "source": ["fsi"]}, "warnings": ["Number of targets for cloud_assets_collection_csp_api_request_duration_seconds_bucket is high: 4"], "hpa": {"ca-collection-fsi-workers": {"min": 1, "max": 100}}, "targets": [{"scrapeUrl": "http://***********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf6ps", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf22g", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-99jvp", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-klvwg", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_csp_api_request_duration_seconds Request durations for each CSP API call, labeled by API and status code.", "# TYPE cloud_assets_collection_csp_api_request_duration_seconds histogram", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"0.005\"} 0", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"0.01\"} 0", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"0.025\"} 0", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"0.05\"} 57", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"0.1\"} 89", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"0.25\"} 99", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"0.5\"} 99", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"1\"} 99", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"2.5\"} 99", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"5\"} 99", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"10\"} 99", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"+Inf\"} 99", "cloud_assets_collection_csp_api_request_duration_seconds_sum{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\"} 6.118128763", "cloud_assets_collection_csp_api_request_duration_seconds_count{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\"} 99"]}], "cardinality_formula": {"per_pod": "1(action labels) * 1(api_status_code labels) * 1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 15(le labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(resource labels) * 1(service labels) * 1(source labels)", "current": "1(action labels) * 1(api_status_code labels) * 1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 15(le labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(resource labels) * 1(service labels) * 1(source labels) * 4(targets)", "min": "1(action labels) * 1(api_status_code labels) * 1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 15(le labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(resource labels) * 1(service labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))", "max": "1(action labels) * 1(api_status_code labels) * 1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 15(le labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(resource labels) * 1(service labels) * 1(source labels) * (100(ca-collection-fsi-workers pods))", "avg": "((1(action labels) * 1(api_status_code labels) * 1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 15(le labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(resource labels) * 1(service labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))) + (1(action labels) * 1(api_status_code labels) * 1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 15(le labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(resource labels) * 1(service labels) * 1(source labels) * (100(ca-collection-fsi-workers pods)))) / 2"}, "cardinality": {"per_pod": 15, "current": 60, "min": 15, "max": 1500, "avg": 757.5}}, {"name": "cloud_assets_collection_csp_api_request_duration_seconds_count", "labels": {"action": ["Get"], "api_status_code": ["200"], "app": ["xdr-st-*************-ca-collection-fsi-workers"], "app_name": ["xdr-st-*************-ca-collection-fsi-workers"], "cloud_provider": ["AWS"], "owner_panw_group": ["platform"], "owner_panw_people_slack_handle_owners_group": ["m<PERSON><PERSON><PERSON>"], "owner_panw_people_slack_handle_team_lead": ["<PERSON><PERSON><PERSON>"], "owner_panw_team": ["asset"], "owner_panw_team_slack_handle": ["platform-collection-owners"], "rate_limit_group": ["shared-gubernator"], "resource": ["Token"], "service": ["api.ecr-public"], "source": ["fsi"]}, "warnings": ["Number of targets for cloud_assets_collection_csp_api_request_duration_seconds_count is high: 4"], "hpa": {"ca-collection-fsi-workers": {"min": 1, "max": 100}}, "targets": [{"scrapeUrl": "http://***********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf6ps", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf22g", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-99jvp", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-klvwg", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_csp_api_request_duration_seconds Request durations for each CSP API call, labeled by API and status code.", "# TYPE cloud_assets_collection_csp_api_request_duration_seconds histogram", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"0.005\"} 0", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"0.01\"} 0", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"0.025\"} 0", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"0.05\"} 57", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"0.1\"} 89", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"0.25\"} 99", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"0.5\"} 99", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"1\"} 99", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"2.5\"} 99", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"5\"} 99", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"10\"} 99", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"+Inf\"} 99", "cloud_assets_collection_csp_api_request_duration_seconds_sum{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\"} 6.118128763", "cloud_assets_collection_csp_api_request_duration_seconds_count{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\"} 99"]}], "cardinality_formula": {"per_pod": "1(action labels) * 1(api_status_code labels) * 1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(resource labels) * 1(service labels) * 1(source labels)", "current": "1(action labels) * 1(api_status_code labels) * 1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(resource labels) * 1(service labels) * 1(source labels) * 4(targets)", "min": "1(action labels) * 1(api_status_code labels) * 1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(resource labels) * 1(service labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))", "max": "1(action labels) * 1(api_status_code labels) * 1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(resource labels) * 1(service labels) * 1(source labels) * (100(ca-collection-fsi-workers pods))", "avg": "((1(action labels) * 1(api_status_code labels) * 1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(resource labels) * 1(service labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))) + (1(action labels) * 1(api_status_code labels) * 1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(resource labels) * 1(service labels) * 1(source labels) * (100(ca-collection-fsi-workers pods)))) / 2"}, "cardinality": {"per_pod": 1, "current": 4, "min": 1, "max": 100, "avg": 50.5}}, {"name": "cloud_assets_collection_csp_api_request_duration_seconds_sum", "labels": {"action": ["Get"], "api_status_code": ["200"], "app": ["xdr-st-*************-ca-collection-fsi-workers"], "app_name": ["xdr-st-*************-ca-collection-fsi-workers"], "cloud_provider": ["AWS"], "owner_panw_group": ["platform"], "owner_panw_people_slack_handle_owners_group": ["m<PERSON><PERSON><PERSON>"], "owner_panw_people_slack_handle_team_lead": ["<PERSON><PERSON><PERSON>"], "owner_panw_team": ["asset"], "owner_panw_team_slack_handle": ["platform-collection-owners"], "rate_limit_group": ["shared-gubernator"], "resource": ["Token"], "service": ["api.ecr-public"], "source": ["fsi"]}, "warnings": ["Number of targets for cloud_assets_collection_csp_api_request_duration_seconds_sum is high: 4"], "hpa": {"ca-collection-fsi-workers": {"min": 1, "max": 100}}, "targets": [{"scrapeUrl": "http://***********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf6ps", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf22g", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-99jvp", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-klvwg", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_csp_api_request_duration_seconds Request durations for each CSP API call, labeled by API and status code.", "# TYPE cloud_assets_collection_csp_api_request_duration_seconds histogram", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"0.005\"} 0", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"0.01\"} 0", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"0.025\"} 0", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"0.05\"} 57", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"0.1\"} 89", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"0.25\"} 99", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"0.5\"} 99", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"1\"} 99", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"2.5\"} 99", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"5\"} 99", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"10\"} 99", "cloud_assets_collection_csp_api_request_duration_seconds_bucket{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\",le=\"+Inf\"} 99", "cloud_assets_collection_csp_api_request_duration_seconds_sum{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\"} 6.118128763", "cloud_assets_collection_csp_api_request_duration_seconds_count{action=\"Get\",api_status_code=\"200\",cloud_provider=\"AWS\",resource=\"Token\",service=\"api.ecr-public\",source=\"fsi\"} 99"]}], "cardinality_formula": {"per_pod": "1(action labels) * 1(api_status_code labels) * 1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(resource labels) * 1(service labels) * 1(source labels)", "current": "1(action labels) * 1(api_status_code labels) * 1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(resource labels) * 1(service labels) * 1(source labels) * 4(targets)", "min": "1(action labels) * 1(api_status_code labels) * 1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(resource labels) * 1(service labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))", "max": "1(action labels) * 1(api_status_code labels) * 1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(resource labels) * 1(service labels) * 1(source labels) * (100(ca-collection-fsi-workers pods))", "avg": "((1(action labels) * 1(api_status_code labels) * 1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(resource labels) * 1(service labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))) + (1(action labels) * 1(api_status_code labels) * 1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(resource labels) * 1(service labels) * 1(source labels) * (100(ca-collection-fsi-workers pods)))) / 2"}, "cardinality": {"per_pod": 1, "current": 4, "min": 1, "max": 100, "avg": 50.5}}, {"name": "cloud_assets_collection_message_processing_duration_seconds_bucket", "labels": {"app": ["xdr-st-*************-ca-collection-fsi-workers"], "app_name": ["xdr-st-*************-ca-collection-fsi-workers"], "le": ["+Inf", "1200.0", "1800.0", "2400.0", "300.0", "3000.0", "3600.0", "60.0", "600.0", "900.0", "60", "300", "600", "900", "1200", "1800", "2400", "3000", "3600"], "message_priority": ["high"], "owner_panw_group": ["platform"], "owner_panw_people_slack_handle_owners_group": ["m<PERSON><PERSON><PERSON>"], "owner_panw_people_slack_handle_team_lead": ["<PERSON><PERSON><PERSON>"], "owner_panw_team": ["asset"], "owner_panw_team_slack_handle": ["platform-collection-owners"], "rate_limit_group": ["shared-gubernator"], "source": ["fsi"]}, "warnings": ["String type is number. Bad practice because it may have too many different values. 'le=600'", "String type is number. Bad practice because it may have too many different values. 'le=900'", "String type is number. Bad practice because it may have too many different values. 'le=1200'", "String type is number. Bad practice because it may have too many different values. 'le=1800'", "String type is number. Bad practice because it may have too many different values. 'le=2400'", "String type is number. Bad practice because it may have too many different values. 'le=3000'", "String type is number. Bad practice because it may have too many different values. 'le=3600'", "Number of targets for cloud_assets_collection_message_processing_duration_seconds_bucket is high: 4"], "hpa": {"ca-collection-fsi-workers": {"min": 1, "max": 100}}, "targets": [{"scrapeUrl": "http://***********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf6ps", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf22g", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_processing_duration_seconds Duration of message processing - without time spent in the queue (in seconds).", "# TYPE cloud_assets_collection_message_processing_duration_seconds histogram", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 158", "cloud_assets_collection_message_processing_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 1160.7593806850002", "cloud_assets_collection_message_processing_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 158"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-99jvp", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_processing_duration_seconds Duration of message processing - without time spent in the queue (in seconds).", "# TYPE cloud_assets_collection_message_processing_duration_seconds histogram", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 18", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 24", "cloud_assets_collection_message_processing_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 470.46686948400003", "cloud_assets_collection_message_processing_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 24"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-klvwg", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_processing_duration_seconds Duration of message processing - without time spent in the queue (in seconds).", "# TYPE cloud_assets_collection_message_processing_duration_seconds histogram", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 527", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 529", "cloud_assets_collection_message_processing_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 3060.5592842250026", "cloud_assets_collection_message_processing_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"60\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"300\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"600\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"900\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"1200\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"1800\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"2400\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"3000\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"3600\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"+Inf\"} 57", "cloud_assets_collection_message_processing_duration_seconds_sum{message_priority=\"high\",source=\"fsi\"} 302.8066676480001", "cloud_assets_collection_message_processing_duration_seconds_count{message_priority=\"high\",source=\"fsi\"} 57"]}], "cardinality_formula": {"per_pod": "1(app labels) * 1(app_name labels) * 19(le labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels)", "current": "1(app labels) * 1(app_name labels) * 19(le labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * 4(targets)", "min": "1(app labels) * 1(app_name labels) * 19(le labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))", "max": "1(app labels) * 1(app_name labels) * 19(le labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (100(ca-collection-fsi-workers pods))", "avg": "((1(app labels) * 1(app_name labels) * 19(le labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))) + (1(app labels) * 1(app_name labels) * 19(le labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (100(ca-collection-fsi-workers pods)))) / 2"}, "cardinality": {"per_pod": 19, "current": 76, "min": 19, "max": 1900, "avg": 959.5}}, {"name": "cloud_assets_collection_message_processing_duration_seconds_count", "labels": {"app": ["xdr-st-*************-ca-collection-fsi-workers"], "app_name": ["xdr-st-*************-ca-collection-fsi-workers"], "message_priority": ["high"], "owner_panw_group": ["platform"], "owner_panw_people_slack_handle_owners_group": ["m<PERSON><PERSON><PERSON>"], "owner_panw_people_slack_handle_team_lead": ["<PERSON><PERSON><PERSON>"], "owner_panw_team": ["asset"], "owner_panw_team_slack_handle": ["platform-collection-owners"], "rate_limit_group": ["shared-gubernator"], "source": ["fsi"]}, "warnings": ["Number of targets for cloud_assets_collection_message_processing_duration_seconds_count is high: 4"], "hpa": {"ca-collection-fsi-workers": {"min": 1, "max": 100}}, "targets": [{"scrapeUrl": "http://***********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf6ps", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf22g", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_processing_duration_seconds Duration of message processing - without time spent in the queue (in seconds).", "# TYPE cloud_assets_collection_message_processing_duration_seconds histogram", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 158", "cloud_assets_collection_message_processing_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 1160.7593806850002", "cloud_assets_collection_message_processing_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 158"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-99jvp", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_processing_duration_seconds Duration of message processing - without time spent in the queue (in seconds).", "# TYPE cloud_assets_collection_message_processing_duration_seconds histogram", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 18", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 24", "cloud_assets_collection_message_processing_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 470.46686948400003", "cloud_assets_collection_message_processing_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 24"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-klvwg", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_processing_duration_seconds Duration of message processing - without time spent in the queue (in seconds).", "# TYPE cloud_assets_collection_message_processing_duration_seconds histogram", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 527", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 529", "cloud_assets_collection_message_processing_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 3060.5592842250026", "cloud_assets_collection_message_processing_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"60\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"300\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"600\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"900\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"1200\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"1800\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"2400\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"3000\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"3600\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"+Inf\"} 57", "cloud_assets_collection_message_processing_duration_seconds_sum{message_priority=\"high\",source=\"fsi\"} 302.8066676480001", "cloud_assets_collection_message_processing_duration_seconds_count{message_priority=\"high\",source=\"fsi\"} 57"]}], "cardinality_formula": {"per_pod": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels)", "current": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * 4(targets)", "min": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))", "max": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (100(ca-collection-fsi-workers pods))", "avg": "((1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))) + (1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (100(ca-collection-fsi-workers pods)))) / 2"}, "cardinality": {"per_pod": 1, "current": 4, "min": 1, "max": 100, "avg": 50.5}}, {"name": "cloud_assets_collection_message_processing_duration_seconds_sum", "labels": {"app": ["xdr-st-*************-ca-collection-fsi-workers"], "app_name": ["xdr-st-*************-ca-collection-fsi-workers"], "message_priority": ["high"], "owner_panw_group": ["platform"], "owner_panw_people_slack_handle_owners_group": ["m<PERSON><PERSON><PERSON>"], "owner_panw_people_slack_handle_team_lead": ["<PERSON><PERSON><PERSON>"], "owner_panw_team": ["asset"], "owner_panw_team_slack_handle": ["platform-collection-owners"], "rate_limit_group": ["shared-gubernator"], "source": ["fsi"]}, "warnings": ["Number of targets for cloud_assets_collection_message_processing_duration_seconds_sum is high: 4"], "hpa": {"ca-collection-fsi-workers": {"min": 1, "max": 100}}, "targets": [{"scrapeUrl": "http://***********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf6ps", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf22g", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_processing_duration_seconds Duration of message processing - without time spent in the queue (in seconds).", "# TYPE cloud_assets_collection_message_processing_duration_seconds histogram", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 158", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 158", "cloud_assets_collection_message_processing_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 1160.7593806850002", "cloud_assets_collection_message_processing_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 158"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-99jvp", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_processing_duration_seconds Duration of message processing - without time spent in the queue (in seconds).", "# TYPE cloud_assets_collection_message_processing_duration_seconds histogram", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 18", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 24", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 24", "cloud_assets_collection_message_processing_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 470.46686948400003", "cloud_assets_collection_message_processing_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 24"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-klvwg", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_processing_duration_seconds Duration of message processing - without time spent in the queue (in seconds).", "# TYPE cloud_assets_collection_message_processing_duration_seconds histogram", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 527", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 529", "cloud_assets_collection_message_processing_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 3060.5592842250026", "cloud_assets_collection_message_processing_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 529", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"60\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"300\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"600\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"900\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"1200\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"1800\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"2400\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"3000\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"3600\"} 57", "cloud_assets_collection_message_processing_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"+Inf\"} 57", "cloud_assets_collection_message_processing_duration_seconds_sum{message_priority=\"high\",source=\"fsi\"} 302.8066676480001", "cloud_assets_collection_message_processing_duration_seconds_count{message_priority=\"high\",source=\"fsi\"} 57"]}], "cardinality_formula": {"per_pod": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels)", "current": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * 4(targets)", "min": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))", "max": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (100(ca-collection-fsi-workers pods))", "avg": "((1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))) + (1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (100(ca-collection-fsi-workers pods)))) / 2"}, "cardinality": {"per_pod": 1, "current": 4, "min": 1, "max": 100, "avg": 50.5}}, {"name": "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket", "labels": {"app": ["xdr-st-*************-ca-collection-fsi-workers"], "app_name": ["xdr-st-*************-ca-collection-fsi-workers"], "le": ["+Inf", "1200.0", "1800.0", "2400.0", "300.0", "3000.0", "3600.0", "60.0", "600.0", "7200.0", "900.0", "60", "300", "600", "900", "1200", "1800", "2400", "3000", "3600", "7200"], "message_priority": ["high"], "owner_panw_group": ["platform"], "owner_panw_people_slack_handle_owners_group": ["m<PERSON><PERSON><PERSON>"], "owner_panw_people_slack_handle_team_lead": ["<PERSON><PERSON><PERSON>"], "owner_panw_team": ["asset"], "owner_panw_team_slack_handle": ["platform-collection-owners"], "rate_limit_group": ["shared-gubernator"], "source": ["fsi"]}, "warnings": ["String type is number. Bad practice because it may have too many different values. 'le=600'", "String type is number. Bad practice because it may have too many different values. 'le=900'", "String type is number. Bad practice because it may have too many different values. 'le=1200'", "String type is number. Bad practice because it may have too many different values. 'le=1800'", "String type is number. Bad practice because it may have too many different values. 'le=2400'", "String type is number. Bad practice because it may have too many different values. 'le=3000'", "String type is number. Bad practice because it may have too many different values. 'le=3600'", "String type is number. Bad practice because it may have too many different values. 'le=7200'", "Average cardinality of cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket is high: 1060.5", "Number of targets for cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket is high: 4"], "hpa": {"ca-collection-fsi-workers": {"min": 1, "max": 100}}, "targets": [{"scrapeUrl": "http://***********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf6ps", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf22g", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_publish_to_processing_complete_duration_seconds Total Duration of message processing - including queue time (in seconds).", "# TYPE cloud_assets_collection_message_publish_to_processing_complete_duration_seconds histogram", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 0", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 0", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"7200\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 68210.50621555897", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 158"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-99jvp", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_publish_to_processing_complete_duration_seconds Total Duration of message processing - including queue time (in seconds).", "# TYPE cloud_assets_collection_message_publish_to_processing_complete_duration_seconds histogram", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 0", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 0", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"7200\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 12974.132142067", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 24"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-klvwg", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_publish_to_processing_complete_duration_seconds Total Duration of message processing - including queue time (in seconds).", "# TYPE cloud_assets_collection_message_publish_to_processing_complete_duration_seconds histogram", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 6", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 290", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"7200\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 147867.66656136798", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"60\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"300\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"600\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"900\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"1200\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"1800\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"2400\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"3000\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"3600\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"7200\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"+Inf\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_sum{message_priority=\"high\",source=\"fsi\"} 1318.381373842", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_count{message_priority=\"high\",source=\"fsi\"} 57"]}], "cardinality_formula": {"per_pod": "1(app labels) * 1(app_name labels) * 21(le labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels)", "current": "1(app labels) * 1(app_name labels) * 21(le labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * 4(targets)", "min": "1(app labels) * 1(app_name labels) * 21(le labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))", "max": "1(app labels) * 1(app_name labels) * 21(le labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (100(ca-collection-fsi-workers pods))", "avg": "((1(app labels) * 1(app_name labels) * 21(le labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))) + (1(app labels) * 1(app_name labels) * 21(le labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (100(ca-collection-fsi-workers pods)))) / 2"}, "cardinality": {"per_pod": 21, "current": 84, "min": 21, "max": 2100, "avg": 1060.5}}, {"name": "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_count", "labels": {"app": ["xdr-st-*************-ca-collection-fsi-workers"], "app_name": ["xdr-st-*************-ca-collection-fsi-workers"], "message_priority": ["high"], "owner_panw_group": ["platform"], "owner_panw_people_slack_handle_owners_group": ["m<PERSON><PERSON><PERSON>"], "owner_panw_people_slack_handle_team_lead": ["<PERSON><PERSON><PERSON>"], "owner_panw_team": ["asset"], "owner_panw_team_slack_handle": ["platform-collection-owners"], "rate_limit_group": ["shared-gubernator"], "source": ["fsi"]}, "warnings": ["Number of targets for cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_count is high: 4"], "hpa": {"ca-collection-fsi-workers": {"min": 1, "max": 100}}, "targets": [{"scrapeUrl": "http://***********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf6ps", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf22g", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_publish_to_processing_complete_duration_seconds Total Duration of message processing - including queue time (in seconds).", "# TYPE cloud_assets_collection_message_publish_to_processing_complete_duration_seconds histogram", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 0", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 0", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"7200\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 68210.50621555897", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 158"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-99jvp", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_publish_to_processing_complete_duration_seconds Total Duration of message processing - including queue time (in seconds).", "# TYPE cloud_assets_collection_message_publish_to_processing_complete_duration_seconds histogram", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 0", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 0", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"7200\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 12974.132142067", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 24"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-klvwg", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_publish_to_processing_complete_duration_seconds Total Duration of message processing - including queue time (in seconds).", "# TYPE cloud_assets_collection_message_publish_to_processing_complete_duration_seconds histogram", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 6", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 290", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"7200\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 147867.66656136798", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"60\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"300\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"600\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"900\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"1200\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"1800\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"2400\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"3000\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"3600\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"7200\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"+Inf\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_sum{message_priority=\"high\",source=\"fsi\"} 1318.381373842", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_count{message_priority=\"high\",source=\"fsi\"} 57"]}], "cardinality_formula": {"per_pod": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels)", "current": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * 4(targets)", "min": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))", "max": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (100(ca-collection-fsi-workers pods))", "avg": "((1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))) + (1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (100(ca-collection-fsi-workers pods)))) / 2"}, "cardinality": {"per_pod": 1, "current": 4, "min": 1, "max": 100, "avg": 50.5}}, {"name": "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_sum", "labels": {"app": ["xdr-st-*************-ca-collection-fsi-workers"], "app_name": ["xdr-st-*************-ca-collection-fsi-workers"], "message_priority": ["high"], "owner_panw_group": ["platform"], "owner_panw_people_slack_handle_owners_group": ["m<PERSON><PERSON><PERSON>"], "owner_panw_people_slack_handle_team_lead": ["<PERSON><PERSON><PERSON>"], "owner_panw_team": ["asset"], "owner_panw_team_slack_handle": ["platform-collection-owners"], "rate_limit_group": ["shared-gubernator"], "source": ["fsi"]}, "warnings": ["Number of targets for cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_sum is high: 4"], "hpa": {"ca-collection-fsi-workers": {"min": 1, "max": 100}}, "targets": [{"scrapeUrl": "http://***********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf6ps", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf22g", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_publish_to_processing_complete_duration_seconds Total Duration of message processing - including queue time (in seconds).", "# TYPE cloud_assets_collection_message_publish_to_processing_complete_duration_seconds histogram", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 0", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 0", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"7200\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 158", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 68210.50621555897", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 158"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-99jvp", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_publish_to_processing_complete_duration_seconds Total Duration of message processing - including queue time (in seconds).", "# TYPE cloud_assets_collection_message_publish_to_processing_complete_duration_seconds histogram", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 0", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 0", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"7200\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 24", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 12974.132142067", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 24"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-klvwg", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_publish_to_processing_complete_duration_seconds Total Duration of message processing - including queue time (in seconds).", "# TYPE cloud_assets_collection_message_publish_to_processing_complete_duration_seconds histogram", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 6", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 290", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"7200\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 147867.66656136798", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 529", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"60\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"300\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"600\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"900\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"1200\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"1800\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"2400\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"3000\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"3600\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"7200\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"+Inf\"} 57", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_sum{message_priority=\"high\",source=\"fsi\"} 1318.381373842", "cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_count{message_priority=\"high\",source=\"fsi\"} 57"]}], "cardinality_formula": {"per_pod": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels)", "current": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * 4(targets)", "min": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))", "max": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (100(ca-collection-fsi-workers pods))", "avg": "((1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))) + (1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (100(ca-collection-fsi-workers pods)))) / 2"}, "cardinality": {"per_pod": 1, "current": 4, "min": 1, "max": 100, "avg": 50.5}}, {"name": "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket", "labels": {"app": ["xdr-st-*************-ca-collection-fsi-workers"], "app_name": ["xdr-st-*************-ca-collection-fsi-workers"], "le": ["+Inf", "1200.0", "1800.0", "2400.0", "300.0", "3000.0", "3600.0", "60.0", "600.0", "7200.0", "900.0", "60", "300", "600", "900", "1200", "1800", "2400", "3000", "3600", "7200"], "message_priority": ["high"], "owner_panw_group": ["platform"], "owner_panw_people_slack_handle_owners_group": ["m<PERSON><PERSON><PERSON>"], "owner_panw_people_slack_handle_team_lead": ["<PERSON><PERSON><PERSON>"], "owner_panw_team": ["asset"], "owner_panw_team_slack_handle": ["platform-collection-owners"], "rate_limit_group": ["shared-gubernator"], "source": ["fsi"]}, "warnings": ["String type is number. Bad practice because it may have too many different values. 'le=600'", "String type is number. Bad practice because it may have too many different values. 'le=900'", "String type is number. Bad practice because it may have too many different values. 'le=1200'", "String type is number. Bad practice because it may have too many different values. 'le=1800'", "String type is number. Bad practice because it may have too many different values. 'le=2400'", "String type is number. Bad practice because it may have too many different values. 'le=3000'", "String type is number. Bad practice because it may have too many different values. 'le=3600'", "String type is number. Bad practice because it may have too many different values. 'le=7200'", "Average cardinality of cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket is high: 1060.5", "Number of targets for cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket is high: 4"], "hpa": {"ca-collection-fsi-workers": {"min": 1, "max": 100}}, "targets": [{"scrapeUrl": "http://***********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf6ps", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf22g", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_publish_to_processing_start_duration_seconds Duration between message publish time to start processing it (in seconds).", "# TYPE cloud_assets_collection_message_publish_to_processing_start_duration_seconds histogram", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 0", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 0", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"7200\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 67049.74683487396", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 158"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-99jvp", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_publish_to_processing_start_duration_seconds Duration between message publish time to start processing it (in seconds).", "# TYPE cloud_assets_collection_message_publish_to_processing_start_duration_seconds histogram", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 0", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 0", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"7200\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 12503.665272583", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 24"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-klvwg", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_publish_to_processing_start_duration_seconds Duration between message publish time to start processing it (in seconds).", "# TYPE cloud_assets_collection_message_publish_to_processing_start_duration_seconds histogram", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 12", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 297", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"7200\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 144807.10727714308", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"60\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"300\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"600\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"900\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"1200\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"1800\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"2400\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"3000\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"3600\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"7200\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"+Inf\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_sum{message_priority=\"high\",source=\"fsi\"} 1015.5747061940002", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_count{message_priority=\"high\",source=\"fsi\"} 57"]}], "cardinality_formula": {"per_pod": "1(app labels) * 1(app_name labels) * 21(le labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels)", "current": "1(app labels) * 1(app_name labels) * 21(le labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * 4(targets)", "min": "1(app labels) * 1(app_name labels) * 21(le labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))", "max": "1(app labels) * 1(app_name labels) * 21(le labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (100(ca-collection-fsi-workers pods))", "avg": "((1(app labels) * 1(app_name labels) * 21(le labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))) + (1(app labels) * 1(app_name labels) * 21(le labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (100(ca-collection-fsi-workers pods)))) / 2"}, "cardinality": {"per_pod": 21, "current": 84, "min": 21, "max": 2100, "avg": 1060.5}}, {"name": "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_count", "labels": {"app": ["xdr-st-*************-ca-collection-fsi-workers"], "app_name": ["xdr-st-*************-ca-collection-fsi-workers"], "message_priority": ["high"], "owner_panw_group": ["platform"], "owner_panw_people_slack_handle_owners_group": ["m<PERSON><PERSON><PERSON>"], "owner_panw_people_slack_handle_team_lead": ["<PERSON><PERSON><PERSON>"], "owner_panw_team": ["asset"], "owner_panw_team_slack_handle": ["platform-collection-owners"], "rate_limit_group": ["shared-gubernator"], "source": ["fsi"]}, "warnings": ["Number of targets for cloud_assets_collection_message_publish_to_processing_start_duration_seconds_count is high: 4"], "hpa": {"ca-collection-fsi-workers": {"min": 1, "max": 100}}, "targets": [{"scrapeUrl": "http://***********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf6ps", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf22g", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_publish_to_processing_start_duration_seconds Duration between message publish time to start processing it (in seconds).", "# TYPE cloud_assets_collection_message_publish_to_processing_start_duration_seconds histogram", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 0", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 0", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"7200\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 67049.74683487396", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 158"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-99jvp", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_publish_to_processing_start_duration_seconds Duration between message publish time to start processing it (in seconds).", "# TYPE cloud_assets_collection_message_publish_to_processing_start_duration_seconds histogram", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 0", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 0", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"7200\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 12503.665272583", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 24"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-klvwg", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_publish_to_processing_start_duration_seconds Duration between message publish time to start processing it (in seconds).", "# TYPE cloud_assets_collection_message_publish_to_processing_start_duration_seconds histogram", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 12", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 297", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"7200\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 144807.10727714308", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"60\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"300\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"600\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"900\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"1200\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"1800\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"2400\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"3000\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"3600\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"7200\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"+Inf\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_sum{message_priority=\"high\",source=\"fsi\"} 1015.5747061940002", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_count{message_priority=\"high\",source=\"fsi\"} 57"]}], "cardinality_formula": {"per_pod": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels)", "current": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * 4(targets)", "min": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))", "max": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (100(ca-collection-fsi-workers pods))", "avg": "((1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))) + (1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (100(ca-collection-fsi-workers pods)))) / 2"}, "cardinality": {"per_pod": 1, "current": 4, "min": 1, "max": 100, "avg": 50.5}}, {"name": "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_sum", "labels": {"app": ["xdr-st-*************-ca-collection-fsi-workers"], "app_name": ["xdr-st-*************-ca-collection-fsi-workers"], "message_priority": ["high"], "owner_panw_group": ["platform"], "owner_panw_people_slack_handle_owners_group": ["m<PERSON><PERSON><PERSON>"], "owner_panw_people_slack_handle_team_lead": ["<PERSON><PERSON><PERSON>"], "owner_panw_team": ["asset"], "owner_panw_team_slack_handle": ["platform-collection-owners"], "rate_limit_group": ["shared-gubernator"], "source": ["fsi"]}, "warnings": ["Number of targets for cloud_assets_collection_message_publish_to_processing_start_duration_seconds_sum is high: 4"], "hpa": {"ca-collection-fsi-workers": {"min": 1, "max": 100}}, "targets": [{"scrapeUrl": "http://***********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf6ps", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf22g", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_publish_to_processing_start_duration_seconds Duration between message publish time to start processing it (in seconds).", "# TYPE cloud_assets_collection_message_publish_to_processing_start_duration_seconds histogram", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 0", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 0", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"7200\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 158", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 67049.74683487396", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 158"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-99jvp", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_publish_to_processing_start_duration_seconds Duration between message publish time to start processing it (in seconds).", "# TYPE cloud_assets_collection_message_publish_to_processing_start_duration_seconds histogram", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 0", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 0", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"7200\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 24", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 12503.665272583", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 24"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-klvwg", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_message_publish_to_processing_start_duration_seconds Duration between message publish time to start processing it (in seconds).", "# TYPE cloud_assets_collection_message_publish_to_processing_start_duration_seconds histogram", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"60\"} 12", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"300\"} 297", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"600\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"900\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1200\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"1800\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"2400\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3000\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"3600\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"7200\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"\",source=\"fsi\",le=\"+Inf\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_sum{message_priority=\"\",source=\"fsi\"} 144807.10727714308", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_count{message_priority=\"\",source=\"fsi\"} 529", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"60\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"300\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"600\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"900\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"1200\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"1800\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"2400\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"3000\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"3600\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"7200\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket{message_priority=\"high\",source=\"fsi\",le=\"+Inf\"} 57", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_sum{message_priority=\"high\",source=\"fsi\"} 1015.5747061940002", "cloud_assets_collection_message_publish_to_processing_start_duration_seconds_count{message_priority=\"high\",source=\"fsi\"} 57"]}], "cardinality_formula": {"per_pod": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels)", "current": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * 4(targets)", "min": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))", "max": "1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (100(ca-collection-fsi-workers pods))", "avg": "((1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))) + (1(app labels) * 1(app_name labels) * 1(message_priority labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (100(ca-collection-fsi-workers pods)))) / 2"}, "cardinality": {"per_pod": 1, "current": 4, "min": 1, "max": 100, "avg": 50.5}}, {"name": "cloud_assets_collection_num_failed_tasks_total", "labels": {"app": ["xdr-st-*************-ca-collection-fsi-workers"], "app_name": ["xdr-st-*************-ca-collection-fsi-workers"], "cloud_provider": ["AWS"], "owner_panw_group": ["platform"], "owner_panw_people_slack_handle_owners_group": ["m<PERSON><PERSON><PERSON>"], "owner_panw_people_slack_handle_team_lead": ["<PERSON><PERSON><PERSON>"], "owner_panw_team": ["asset"], "owner_panw_team_slack_handle": ["platform-collection-owners"], "rate_limit_group": ["shared-gubernator"], "source": ["fsi"]}, "warnings": ["Number of targets for cloud_assets_collection_num_failed_tasks_total is high: 4"], "hpa": {"ca-collection-fsi-workers": {"min": 1, "max": 100}}, "targets": [{"scrapeUrl": "http://***********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf6ps", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf22g", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_num_failed_tasks_total Number of FSI/EAI tasks that failed. Note that for keeping low cardinality FSI task is per all Regions, meaning one region failure means a failed task", "# TYPE cloud_assets_collection_num_failed_tasks_total counter", "cloud_assets_collection_num_failed_tasks_total{cloud_provider=\"AWS\",source=\"fsi\"} 153"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-99jvp", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_num_failed_tasks_total Number of FSI/EAI tasks that failed. Note that for keeping low cardinality FSI task is per all Regions, meaning one region failure means a failed task", "# TYPE cloud_assets_collection_num_failed_tasks_total counter", "cloud_assets_collection_num_failed_tasks_total{cloud_provider=\"AWS\",source=\"fsi\"} 24"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-klvwg", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_num_failed_tasks_total Number of FSI/EAI tasks that failed. Note that for keeping low cardinality FSI task is per all Regions, meaning one region failure means a failed task", "# TYPE cloud_assets_collection_num_failed_tasks_total counter", "cloud_assets_collection_num_failed_tasks_total{cloud_provider=\"AWS\",source=\"fsi\"} 578"]}], "cardinality_formula": {"per_pod": "1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels)", "current": "1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * 4(targets)", "min": "1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))", "max": "1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (100(ca-collection-fsi-workers pods))", "avg": "((1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))) + (1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (100(ca-collection-fsi-workers pods)))) / 2"}, "cardinality": {"per_pod": 1, "current": 4, "min": 1, "max": 100, "avg": 50.5}}, {"name": "cloud_assets_collection_num_successful_tasks_total", "labels": {"app": ["xdr-st-*************-ca-collection-fsi-workers"], "app_name": ["xdr-st-*************-ca-collection-fsi-workers"], "cloud_provider": ["AWS"], "owner_panw_group": ["platform"], "owner_panw_people_slack_handle_owners_group": ["m<PERSON><PERSON><PERSON>"], "owner_panw_people_slack_handle_team_lead": ["<PERSON><PERSON><PERSON>"], "owner_panw_team": ["asset"], "owner_panw_team_slack_handle": ["platform-collection-owners"], "rate_limit_group": ["shared-gubernator"], "source": ["fsi"]}, "warnings": ["Number of targets for cloud_assets_collection_num_successful_tasks_total is high: 4"], "hpa": {"ca-collection-fsi-workers": {"min": 1, "max": 100}}, "targets": [{"scrapeUrl": "http://***********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf6ps", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf22g", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_num_successful_tasks_total Number of FSI/EAI tasks that were successful. Note that for keeping low cardinality FSI task is per all Regions, meaning one region failure means a failed", "# TYPE cloud_assets_collection_num_successful_tasks_total counter", "cloud_assets_collection_num_successful_tasks_total{cloud_provider=\"AWS\",source=\"fsi\"} 5"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-99jvp", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-klvwg", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_num_successful_tasks_total Number of FSI/EAI tasks that were successful. Note that for keeping low cardinality FSI task is per all Regions, meaning one region failure means a failed", "# TYPE cloud_assets_collection_num_successful_tasks_total counter", "cloud_assets_collection_num_successful_tasks_total{cloud_provider=\"AWS\",source=\"fsi\"} 8"]}], "cardinality_formula": {"per_pod": "1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels)", "current": "1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * 4(targets)", "min": "1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))", "max": "1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (100(ca-collection-fsi-workers pods))", "avg": "((1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (1(ca-collection-fsi-workers pods))) + (1(app labels) * 1(app_name labels) * 1(cloud_provider labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 1(rate_limit_group labels) * 1(source labels) * (100(ca-collection-fsi-workers pods)))) / 2"}, "cardinality": {"per_pod": 1, "current": 4, "min": 1, "max": 100, "avg": 50.5}}, {"name": "cloud_assets_collection_platform_api_request_duration_seconds_bucket", "labels": {"api_path": ["http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?", "http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi", "http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single", "http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?", "http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?"], "api_status_code": ["200"], "app": ["xdr-st-*************-ca-collection-coordinator", "xdr-st-*************-ca-collection-eai-workers", "xdr-st-*************-ca-collection-fsi-workers"], "le": ["+Inf", "0.005", "0.01", "0.025", "0.05", "0.1", "0.25", "0.5", "1.0", "10.0", "2.5", "5.0", "1", "5", "10"], "owner_panw_group": ["platform"], "owner_panw_people_slack_handle_owners_group": ["m<PERSON><PERSON><PERSON>"], "owner_panw_people_slack_handle_team_lead": ["<PERSON><PERSON><PERSON>"], "owner_panw_team": ["asset"], "owner_panw_team_slack_handle": ["platform-collection-owners"], "source": ["coordinator", "eai", "fsi"], "app_name": ["xdr-st-*************-ca-collection-eai-workers", "xdr-st-*************-ca-collection-fsi-workers"], "rate_limit_group": ["shared-gubernator"]}, "warnings": ["Every unique combination of key-value label pairs represents a new time series, which can dramatically increase the amount of data stored. Do not use labels to store dimensions with high cardinality (many different label values), such as user IDs, email addresses, or other unbounded sets of values. 'api_path'", "Cardinality of cloud_assets_collection_platform_api_request_duration_seconds_bucket is high: 1350", "Average cardinality of cloud_assets_collection_platform_api_request_duration_seconds_bucket is high: 74925.0", "Number of targets for cloud_assets_collection_platform_api_request_duration_seconds_bucket is high: 6"], "hpa": {"ca-collection-coordinator": {"min": 1, "max": 5}, "ca-collection-eai-workers": {"min": 1, "max": 3}, "ca-collection-fsi-workers": {"min": 1, "max": 100}}, "targets": [{"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-coordinator-8cd98745-gv4nv", "app": "xdr-st-*************-ca-collection-coordinator", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "xdr-st-*************-ca-collection-coordinator"}], "raw_metric": ["# HELP cloud_assets_collection_platform_api_request_duration_seconds Request durations for each Platform API call, labeled by API and status code", "# TYPE cloud_assets_collection_platform_api_request_duration_seconds histogram", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.025\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.05\"} 2", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.1\"} 2", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.25\"} 3", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.5\"} 4", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"1\"} 4", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"2.5\"} 4", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"5\"} 4", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"10\"} 4", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"+Inf\"} 4", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\"} 0.*****************", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\"} 4", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"0.025\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"0.05\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"0.1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"0.25\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\"} 0.01255991", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"0.025\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"0.05\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"0.1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"0.25\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\"} 0.013411842", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.05\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.25\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\"} 0.056825891", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.05\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.1\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.25\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\"} 0.325675393", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\"} 1"]}, {"scrapeUrl": "http://10.12.4.50:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-eai-workers-758464f7f-9djrs", "app": "xdr-st-*************-ca-collection-eai-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-dev-platform-v4.3.0-1206-g35c3293", "container": "ca-collection-eai-workers"}], "raw_metric": ["# HELP cloud_assets_collection_platform_api_request_duration_seconds Request durations for each Platform API call, labeled by API and status code", "# TYPE cloud_assets_collection_platform_api_request_duration_seconds histogram", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"0.05\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"0.1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"0.25\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\"} 0.044123444", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"0.05\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"0.1\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"0.25\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"0.5\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\"} 0.593694996", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\"} 1"]}, {"scrapeUrl": "http://***********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf6ps", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf22g", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_platform_api_request_duration_seconds Request durations for each Platform API call, labeled by API and status code", "# TYPE cloud_assets_collection_platform_api_request_duration_seconds histogram", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.05\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.25\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\"} 0.045645077", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.05\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.1\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.25\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.5\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\"} 0.828343715", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\"} 1"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-99jvp", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_platform_api_request_duration_seconds Request durations for each Platform API call, labeled by API and status code", "# TYPE cloud_assets_collection_platform_api_request_duration_seconds histogram", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.05\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.25\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\"} 0.047908313", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.05\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.1\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.25\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.5\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\"} 0.988105721", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\"} 1"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-klvwg", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_platform_api_request_duration_seconds Request durations for each Platform API call, labeled by API and status code", "# TYPE cloud_assets_collection_platform_api_request_duration_seconds histogram", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.05\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.25\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\"} 0.05167335", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.05\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.1\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.25\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\"} 0.416687488", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\"} 1"]}], "cardinality_formula": {"per_pod": "5(api_path labels) * 1(api_status_code labels) * 3(app labels) * 15(le labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 3(source labels) * 2(app_name labels) * 1(rate_limit_group labels)", "current": "5(api_path labels) * 1(api_status_code labels) * 3(app labels) * 15(le labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 3(source labels) * 2(app_name labels) * 1(rate_limit_group labels) * 6(targets)", "min": "5(api_path labels) * 1(api_status_code labels) * 3(app labels) * 15(le labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 3(source labels) * 2(app_name labels) * 1(rate_limit_group labels) * (1(ca-collection-coordinator pods) + 1(ca-collection-eai-workers pods) + 1(ca-collection-fsi-workers pods))", "max": "5(api_path labels) * 1(api_status_code labels) * 3(app labels) * 15(le labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 3(source labels) * 2(app_name labels) * 1(rate_limit_group labels) * (5(ca-collection-coordinator pods) + 3(ca-collection-eai-workers pods) + 100(ca-collection-fsi-workers pods))", "avg": "((5(api_path labels) * 1(api_status_code labels) * 3(app labels) * 15(le labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 3(source labels) * 2(app_name labels) * 1(rate_limit_group labels) * (1(ca-collection-coordinator pods) + 1(ca-collection-eai-workers pods) + 1(ca-collection-fsi-workers pods))) + (5(api_path labels) * 1(api_status_code labels) * 3(app labels) * 15(le labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 3(source labels) * 2(app_name labels) * 1(rate_limit_group labels) * (5(ca-collection-coordinator pods) + 3(ca-collection-eai-workers pods) + 100(ca-collection-fsi-workers pods)))) / 2"}, "cardinality": {"per_pod": 1350, "current": 8100, "min": 4050, "max": 145800, "avg": 74925.0}}, {"name": "cloud_assets_collection_platform_api_request_duration_seconds_count", "labels": {"api_path": ["http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?", "http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi", "http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single", "http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?", "http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?"], "api_status_code": ["200"], "app": ["xdr-st-*************-ca-collection-coordinator", "xdr-st-*************-ca-collection-eai-workers", "xdr-st-*************-ca-collection-fsi-workers"], "owner_panw_group": ["platform"], "owner_panw_people_slack_handle_owners_group": ["m<PERSON><PERSON><PERSON>"], "owner_panw_people_slack_handle_team_lead": ["<PERSON><PERSON><PERSON>"], "owner_panw_team": ["asset"], "owner_panw_team_slack_handle": ["platform-collection-owners"], "source": ["coordinator", "eai", "fsi"], "app_name": ["xdr-st-*************-ca-collection-eai-workers", "xdr-st-*************-ca-collection-fsi-workers"], "rate_limit_group": ["shared-gubernator"]}, "warnings": ["Every unique combination of key-value label pairs represents a new time series, which can dramatically increase the amount of data stored. Do not use labels to store dimensions with high cardinality (many different label values), such as user IDs, email addresses, or other unbounded sets of values. 'api_path'", "Cardinality of cloud_assets_collection_platform_api_request_duration_seconds_count is high: 90", "Average cardinality of cloud_assets_collection_platform_api_request_duration_seconds_count is high: 4995.0", "Number of targets for cloud_assets_collection_platform_api_request_duration_seconds_count is high: 6"], "hpa": {"ca-collection-coordinator": {"min": 1, "max": 5}, "ca-collection-eai-workers": {"min": 1, "max": 3}, "ca-collection-fsi-workers": {"min": 1, "max": 100}}, "targets": [{"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-coordinator-8cd98745-gv4nv", "app": "xdr-st-*************-ca-collection-coordinator", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "xdr-st-*************-ca-collection-coordinator"}], "raw_metric": ["# HELP cloud_assets_collection_platform_api_request_duration_seconds Request durations for each Platform API call, labeled by API and status code", "# TYPE cloud_assets_collection_platform_api_request_duration_seconds histogram", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.025\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.05\"} 2", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.1\"} 2", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.25\"} 3", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.5\"} 4", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"1\"} 4", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"2.5\"} 4", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"5\"} 4", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"10\"} 4", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"+Inf\"} 4", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\"} 0.*****************", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\"} 4", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"0.025\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"0.05\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"0.1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"0.25\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\"} 0.01255991", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"0.025\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"0.05\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"0.1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"0.25\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\"} 0.013411842", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.05\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.25\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\"} 0.056825891", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.05\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.1\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.25\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\"} 0.325675393", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\"} 1"]}, {"scrapeUrl": "http://10.12.4.50:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-eai-workers-758464f7f-9djrs", "app": "xdr-st-*************-ca-collection-eai-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-dev-platform-v4.3.0-1206-g35c3293", "container": "ca-collection-eai-workers"}], "raw_metric": ["# HELP cloud_assets_collection_platform_api_request_duration_seconds Request durations for each Platform API call, labeled by API and status code", "# TYPE cloud_assets_collection_platform_api_request_duration_seconds histogram", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"0.05\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"0.1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"0.25\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\"} 0.044123444", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"0.05\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"0.1\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"0.25\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"0.5\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\"} 0.593694996", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\"} 1"]}, {"scrapeUrl": "http://***********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf6ps", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf22g", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_platform_api_request_duration_seconds Request durations for each Platform API call, labeled by API and status code", "# TYPE cloud_assets_collection_platform_api_request_duration_seconds histogram", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.05\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.25\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\"} 0.045645077", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.05\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.1\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.25\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.5\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\"} 0.828343715", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\"} 1"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-99jvp", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_platform_api_request_duration_seconds Request durations for each Platform API call, labeled by API and status code", "# TYPE cloud_assets_collection_platform_api_request_duration_seconds histogram", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.05\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.25\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\"} 0.047908313", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.05\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.1\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.25\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.5\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\"} 0.988105721", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\"} 1"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-klvwg", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_platform_api_request_duration_seconds Request durations for each Platform API call, labeled by API and status code", "# TYPE cloud_assets_collection_platform_api_request_duration_seconds histogram", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.05\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.25\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\"} 0.05167335", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.05\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.1\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.25\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\"} 0.416687488", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\"} 1"]}], "cardinality_formula": {"per_pod": "5(api_path labels) * 1(api_status_code labels) * 3(app labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 3(source labels) * 2(app_name labels) * 1(rate_limit_group labels)", "current": "5(api_path labels) * 1(api_status_code labels) * 3(app labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 3(source labels) * 2(app_name labels) * 1(rate_limit_group labels) * 6(targets)", "min": "5(api_path labels) * 1(api_status_code labels) * 3(app labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 3(source labels) * 2(app_name labels) * 1(rate_limit_group labels) * (1(ca-collection-coordinator pods) + 1(ca-collection-eai-workers pods) + 1(ca-collection-fsi-workers pods))", "max": "5(api_path labels) * 1(api_status_code labels) * 3(app labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 3(source labels) * 2(app_name labels) * 1(rate_limit_group labels) * (5(ca-collection-coordinator pods) + 3(ca-collection-eai-workers pods) + 100(ca-collection-fsi-workers pods))", "avg": "((5(api_path labels) * 1(api_status_code labels) * 3(app labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 3(source labels) * 2(app_name labels) * 1(rate_limit_group labels) * (1(ca-collection-coordinator pods) + 1(ca-collection-eai-workers pods) + 1(ca-collection-fsi-workers pods))) + (5(api_path labels) * 1(api_status_code labels) * 3(app labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 3(source labels) * 2(app_name labels) * 1(rate_limit_group labels) * (5(ca-collection-coordinator pods) + 3(ca-collection-eai-workers pods) + 100(ca-collection-fsi-workers pods)))) / 2"}, "cardinality": {"per_pod": 90, "current": 540, "min": 270, "max": 9720, "avg": 4995.0}}, {"name": "cloud_assets_collection_platform_api_request_duration_seconds_sum", "labels": {"api_path": ["http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?", "http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi", "http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single", "http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?", "http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?"], "api_status_code": ["200"], "app": ["xdr-st-*************-ca-collection-coordinator", "xdr-st-*************-ca-collection-eai-workers", "xdr-st-*************-ca-collection-fsi-workers"], "owner_panw_group": ["platform"], "owner_panw_people_slack_handle_owners_group": ["m<PERSON><PERSON><PERSON>"], "owner_panw_people_slack_handle_team_lead": ["<PERSON><PERSON><PERSON>"], "owner_panw_team": ["asset"], "owner_panw_team_slack_handle": ["platform-collection-owners"], "source": ["coordinator", "eai", "fsi"], "app_name": ["xdr-st-*************-ca-collection-eai-workers", "xdr-st-*************-ca-collection-fsi-workers"], "rate_limit_group": ["shared-gubernator"]}, "warnings": ["Every unique combination of key-value label pairs represents a new time series, which can dramatically increase the amount of data stored. Do not use labels to store dimensions with high cardinality (many different label values), such as user IDs, email addresses, or other unbounded sets of values. 'api_path'", "Cardinality of cloud_assets_collection_platform_api_request_duration_seconds_sum is high: 90", "Average cardinality of cloud_assets_collection_platform_api_request_duration_seconds_sum is high: 4995.0", "Number of targets for cloud_assets_collection_platform_api_request_duration_seconds_sum is high: 6"], "hpa": {"ca-collection-coordinator": {"min": 1, "max": 5}, "ca-collection-eai-workers": {"min": 1, "max": 3}, "ca-collection-fsi-workers": {"min": 1, "max": 100}}, "targets": [{"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-coordinator-8cd98745-gv4nv", "app": "xdr-st-*************-ca-collection-coordinator", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "xdr-st-*************-ca-collection-coordinator"}], "raw_metric": ["# HELP cloud_assets_collection_platform_api_request_duration_seconds Request durations for each Platform API call, labeled by API and status code", "# TYPE cloud_assets_collection_platform_api_request_duration_seconds histogram", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.025\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.05\"} 2", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.1\"} 2", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.25\"} 3", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.5\"} 4", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"1\"} 4", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"2.5\"} 4", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"5\"} 4", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"10\"} 4", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\",le=\"+Inf\"} 4", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\"} 0.*****************", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/accounts?\",api_status_code=\"200\",source=\"coordinator\"} 4", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"0.025\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"0.05\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"0.1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"0.25\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\"} 0.01255991", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=multi\",api_status_code=\"200\",source=\"coordinator\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"0.025\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"0.05\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"0.1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"0.25\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\"} 0.013411842", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/api/v0/onboarding/regions?cloud_type=AWS&region_type=single\",api_status_code=\"200\",source=\"coordinator\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.05\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.25\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\"} 0.056825891", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"coordinator\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.05\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.1\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.25\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\"} 0.325675393", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"coordinator\"} 1"]}, {"scrapeUrl": "http://10.12.4.50:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-eai-workers-758464f7f-9djrs", "app": "xdr-st-*************-ca-collection-eai-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-dev-platform-v4.3.0-1206-g35c3293", "container": "ca-collection-eai-workers"}], "raw_metric": ["# HELP cloud_assets_collection_platform_api_request_duration_seconds Request durations for each Platform API call, labeled by API and status code", "# TYPE cloud_assets_collection_platform_api_request_duration_seconds histogram", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"0.05\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"0.1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"0.25\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\"} 0.044123444", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"eai\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"0.05\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"0.1\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"0.25\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"0.5\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\"} 0.593694996", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"eai\"} 1"]}, {"scrapeUrl": "http://***********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf6ps", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf22g", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_platform_api_request_duration_seconds Request durations for each Platform API call, labeled by API and status code", "# TYPE cloud_assets_collection_platform_api_request_duration_seconds histogram", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.05\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.25\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\"} 0.045645077", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.05\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.1\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.25\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.5\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\"} 0.828343715", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\"} 1"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-99jvp", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_platform_api_request_duration_seconds Request durations for each Platform API call, labeled by API and status code", "# TYPE cloud_assets_collection_platform_api_request_duration_seconds histogram", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.05\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.25\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\"} 0.047908313", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.05\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.1\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.25\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.5\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\"} 0.988105721", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\"} 1"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-klvwg", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_platform_api_request_duration_seconds Request durations for each Platform API call, labeled by API and status code", "# TYPE cloud_assets_collection_platform_api_request_duration_seconds histogram", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.05\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.25\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\"} 0.05167335", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/ping/?\",api_status_code=\"200\",source=\"fsi\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.005\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.01\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.025\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.05\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.1\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.25\"} 0", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"0.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"1\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"2.5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"5\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"10\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_bucket{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\",le=\"+Inf\"} 1", "cloud_assets_collection_platform_api_request_duration_seconds_sum{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\"} 0.416687488", "cloud_assets_collection_platform_api_request_duration_seconds_count{api_path=\"http://xdr-st-*************-platform.xdr-st.svc.cluster.local:8000/road-block/?\",api_status_code=\"200\",source=\"fsi\"} 1"]}], "cardinality_formula": {"per_pod": "5(api_path labels) * 1(api_status_code labels) * 3(app labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 3(source labels) * 2(app_name labels) * 1(rate_limit_group labels)", "current": "5(api_path labels) * 1(api_status_code labels) * 3(app labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 3(source labels) * 2(app_name labels) * 1(rate_limit_group labels) * 6(targets)", "min": "5(api_path labels) * 1(api_status_code labels) * 3(app labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 3(source labels) * 2(app_name labels) * 1(rate_limit_group labels) * (1(ca-collection-coordinator pods) + 1(ca-collection-eai-workers pods) + 1(ca-collection-fsi-workers pods))", "max": "5(api_path labels) * 1(api_status_code labels) * 3(app labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 3(source labels) * 2(app_name labels) * 1(rate_limit_group labels) * (5(ca-collection-coordinator pods) + 3(ca-collection-eai-workers pods) + 100(ca-collection-fsi-workers pods))", "avg": "((5(api_path labels) * 1(api_status_code labels) * 3(app labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 3(source labels) * 2(app_name labels) * 1(rate_limit_group labels) * (1(ca-collection-coordinator pods) + 1(ca-collection-eai-workers pods) + 1(ca-collection-fsi-workers pods))) + (5(api_path labels) * 1(api_status_code labels) * 3(app labels) * 1(owner_panw_group labels) * 1(owner_panw_people_slack_handle_owners_group labels) * 1(owner_panw_people_slack_handle_team_lead labels) * 1(owner_panw_team labels) * 1(owner_panw_team_slack_handle labels) * 3(source labels) * 2(app_name labels) * 1(rate_limit_group labels) * (5(ca-collection-coordinator pods) + 3(ca-collection-eai-workers pods) + 100(ca-collection-fsi-workers pods)))) / 2"}, "cardinality": {"per_pod": 90, "current": 540, "min": 270, "max": 9720, "avg": 4995.0}}, {"name": "cloud_assets_collection_num_assets_processed_total", "labels": {"account_id": ["************"], "asset_type": ["aws-access-analyzer", "aws-account-management-alternate-contact", "aws-acm-describe-certificate", "aws-api-gateway-authorizer", "aws-apigateway-domain-name", "aws-apigateway-method", "aws-apigateway-spec-export", "aws-apigatewayv2-api", "aws-app-stream-stack", "aws-appflow-flow", "aws-apprunner-auto-scaling-configuration", "aws-appsync-graphql-api", "aws-bedrock-agent-alias", "aws-bedrock-inference-profile", "aws-budgets-budget", "aws-chime-voice-connector", "aws-cloudformation-describe-stacks", "aws-cloudfront-response-headers-policy", "aws-cloudtrail-describe-trails", "aws-cloudtrail-get-event-selectors", "aws-cloudwatch-insight-rule", "aws-cloudwatch-log-group", "aws-code-artifact-domain", "aws-code-artifact-repository", "aws-code-build-project", "aws-code-commit-repository", "aws-code-pipeline-pipeline", "aws-cognito-sync-pool-usage", "aws-comprehend-document-classifier", "aws-comprehendmedical-entities-detection-v2-jobs", "aws-configservice-aggregator", "aws-configservice-config-rules", "aws-datasync-agent", "aws-datasync-location", "aws-datasync-task", "aws-datasync-task-execution", "aws-dax-cluster", "aws-dax-parameter-group", "aws-describe-mount-targets", "aws-describe-workspace-directories", "aws-describe-workspaces", "aws-devops-guru-service-integration", "aws-dms-certificate", "aws-docdb-db-cluster", "aws-docdb-db-cluster-parameter-group", "aws-ds-directory", "aws-ec2-autoscaling-launch-configuration", "aws-ec2-client-vpn-endpoint", "aws-ec2-describe-network-acls", "aws-ec2-describe-snapshots", "aws-ec2-describe-subnets", "aws-ec2-describe-vpn-connections", "aws-ec2-describe-vpn-gateways-summary", "aws-ec2-ebs-encryption", "aws-ec2-traffic-mirroring", "aws-ec2-vpc-endpoint-connection-notification", "aws-ec2-vpc-endpoint-service", "aws-ec2-vpc-ipam", "aws-ec2-vpc-stats", "aws-ec2-vpn-connections-summary", "aws-ecr-get-repository-policy", "aws-ecr-public-repositories", "aws-ecs-cluster", "aws-efs-access-point", "aws-eks-fargate-profile", "aws-elasticache-cache-clusters", "aws-elasticache-describe-replication-groups", "aws-elasticache-reserved-cache-nodes", "aws-elasticache-user", "aws-elasticbeanstalk-configuration-settings", "aws-elasticbeanstalk-environment", "aws-elb-describe-load-balancers", "aws-elbv2-describe-load-balancers", "aws-elbv2-target-group", "aws-emr-security-configuration", "aws-es-describe-elasticsearch-domain", "aws-forecast-dataset", "aws-forecast-predictor", "aws-fsx-file-system", "aws-global-accelerator-accelerator", "aws-glue-connection", "aws-glue-data-brew-job", "aws-glue-schema", "aws-grafana-workspace", "aws-greengrass-core-definitions", "aws-iam-get-account-password-policy", "aws-iam-get-credential-report", "aws-iam-identity-center-account-assignments", "aws-iam-list-server-certificates", "aws-iam-list-users", "aws-imagebuilder-image-pipeline", "aws-imagebuilder-infrastructure-configuration", "aws-inspector-v2-account-status", "aws-inspector-v2-permission", "aws-iotfleetwise-signal-catalogs", "aws-kendra-index", "aws-kms-get-key-rotation-status", "aws-lex-bot", "aws-lexv2-bot", "aws-lookoutequipment-datasets", "aws-lookoutvision-projects", "aws-macie2-administrator-account", "aws-macie2-session", "aws-mediastore-container", "aws-mgn-launch-configuration-template", "aws-mq-broker", "aws-network-manager-core-network", "aws-networkfirewall-firewall", "aws-networkfirewall-firewall-policy", "aws-opensearch-serverless-collection", "aws-opsworks-user-profiles", "aws-pinpoint-email-channel", "aws-prometheus-workspace", "aws-quicksight-datasource", "aws-rds-option-group", "aws-redshift-describe-clusters", "aws-route53-health-check", "aws-s3-access-point", "aws-sagemaker-endpoint", "aws-sagemaker-notebook-instance", "aws-sagemaker-processing-job", "aws-sagemaker-studio-lifecycle-config", "aws-securityhub-standards", "aws-service-quota", "aws-servicecatalog-appregistry-applications", "aws-shield-advanced-status", "aws-shield-protection-groups", "aws-sns-platform-application", "aws-ssm-inventory-instance-information", "aws-ssm-parameter", "aws-storage-gateway-fileshare", "aws-storage-gateway-information", "aws-swf-domains", "aws-vpc-managed-prefix-list", "aws-vpc-transit-gateway-route-table", "aws-waf-classic-global-ip-set", "aws-waf-v2-global-web-acl-resource", "aws-well-architected-tool-workload", "aws-workspace-ip-group", "aws-xray-encryption-config", "aws-audit-manager-assessment", "aws-cloud9-environment", "aws-comprehend-entities-detection-jobs", "aws-direct-connect-connection", "aws-dms-endpoint", "aws-dms-replication-task", "aws-ec2-launch-template", "aws-fms-admin-account", "aws-greengrass-groups", "aws-iot-analytics-channel", "aws-lambda-layer-version", "aws-migration-hub-home-region-control", "aws-ram-resource-share", "aws-route53-domain", "aws-servicecatalog-appregistry-attribute-groups", "aws-ses-identities", "aws-ssm-resource-compliance-summary", "aws-transfer-family-server", "aws-vpc-dhcp-options", "aws-workspace-bundle", "aws-account-contact-information", "aws-acm-pca-certificate-authority", "aws-amplify-app", "aws-apigateway-get-rest-apis", "aws-apigateway-get-stages", "aws-app-stream-fleet", "aws-app-stream-usage-report-subscription", "aws-application-autoscaling-scaling-policy", "aws-appmesh-mesh", "aws-apprunner-service", "aws-audit-manager-control", "aws-backup-protected-resources", "aws-batch-job-definition", "aws-batch-job-queue", "aws-bedrock-custom-model", "aws-bedrock-foundation-model", "aws-bedrock-provisioned-model-throughput", "aws-cache-engine-versions", "aws-cloudfront-list-distributions", "aws-cloudfront-origin-access-control", "aws-cloudhsm-cluster", "aws-cloudsearch-domain", "aws-cloudwatch-describe-alarms", "aws-code-build-source-credential", "aws-code-pipeline-webhook", "aws-cognito-identity-pool", "aws-cognito-user-pool", "aws-comprehend-document-classifier-summary", "aws-configservice-describe-configuration-recorders", "aws-connect-instance", "aws-costexplorer-cost-and-usage", "aws-datapipeline-pipeline", "aws-describe-account-attributes", "aws-describe-auto-scaling-groups", "aws-describe-ssl-policies", "aws-describe-vpc-endpoints", "aws-detective-datasource-package", "aws-direct-connect-gateway", "aws-dms-replication-instance", "aws-drs-job", "aws-drs-source-server", "aws-dynamodb-describe-table", "aws-ec2-customer-gateway", "aws-ec2-describe-flow-logs", "aws-ec2-describe-instances", "aws-ec2-describe-internet-gateways", "aws-ec2-describe-network-interfaces", "aws-ec2-describe-route-tables", "aws-ec2-describe-security-groups", "aws-ec2-describe-volumes", "aws-ec2-describe-vpc-peering-connections", "aws-ec2-describe-vpcs", "aws-ec2-describe-vpn-gateways", "aws-ec2-elastic-address", "aws-ec2-key-pair", "aws-ecr-image", "aws-ecr-public-image", "aws-ecr-public-registry", "aws-ecr-registry", "aws-ecr-registry-scanning-configuration", "aws-eks-describe-cluster", "aws-eks-node-group", "aws-elasticache-snapshots", "aws-elasticache-subnet-groups", "aws-elasticbeanstalk-applications", "aws-elbv2-target-health", "aws-emr-describe-cluster", "aws-emr-public-access-block", "aws-events-eventbus", "aws-events-rule", "aws-fsx-backup", "aws-glacier-get-vault-lock", "aws-glacier-vault", "aws-glue-crawler", "aws-glue-datacatalog", "aws-glue-resource-policy", "aws-glue-security-configuration", "aws-guardduty-organization-configuration", "aws-iam-get-account-summary", "aws-iam-get-policy-version", "aws-iam-list-attached-user-policies", "aws-iam-list-groups", "aws-iam-list-roles", "aws-iam-list-user-policies", "aws-iam-list-virtual-mfa-devices", "aws-iam-saml-provider", "aws-iam-service-last-accessed-details", "aws-identity-store-group-members", "aws-identity-store-user", "aws-imagebuilder-component", "aws-imagebuilder-image-recipe", "aws-inspector-v2-coverage", "aws-inspector-v2-filter", "aws-iot-account-audit-configuration", "aws-iot-domain-configuration", "aws-iot-events-inputs", "aws-kinesis-list-streams", "aws-lake-formation-setting", "aws-lambda-code-signing-config", "aws-lambda-list-functions", "aws-lightsail-instance", "aws-logs-describe-metric-filters", "aws-macie2-classification-job", "aws-managed-blockchain-networks", "aws-memorydb-cluster", "aws-memorydb-parameter-group", "aws-msk-cluster", "aws-mwaa-environment", "aws-neptune-db-cluster", "aws-network-firewall-rule-group", "aws-network-manager-global-network", "aws-organization", "aws-organizations-account", "aws-organizations-root", "aws-organizations-scp", "aws-organizations-tag-policy", "aws-polly-speech-synthesis-task", "aws-qldb-ledger", "aws-quicksight-account-setting", "aws-quicksight-dataset", "aws-ram-principal", "aws-rds-db-cluster", "aws-rds-db-cluster-parameter-group", "aws-rds-db-cluster-snapshots", "aws-rds-describe-db-instances", "aws-rds-describe-db-parameter-groups", "aws-rds-describe-db-snapshots", "aws-rds-describe-event-subscriptions", "aws-region", "aws-resiliencehub-apps", "aws-route53-list-hosted-zones", "aws-route53-query-logging-config", "aws-route53resolver-query-logging-config", "aws-route53resolver-query-logging-config-association", "aws-s3-batch-operation", "aws-s3api-get-bucket-acl", "aws-s3control-public-access-block", "aws-sagemaker-app", "aws-sagemaker-code-repository", "aws-sagemaker-domain", "aws-sagemaker-endpoint-config", "aws-sagemaker-hyper-parameter-tuning-job", "aws-sagemaker-model", "aws-sagemaker-notebook-instance-lifecycle-config", "aws-sagemaker-training-job", "aws-sagemaker-transform-job", "aws-sagemaker-user-profile", "aws-secretsmanager-describe-secret", "aws-securityhub-enabled-standards", "aws-securityhub-hub", "aws-serverlessrepo-application", "aws-servicecatalog-portfolios", "aws-servicediscovery-namespaces", "aws-shield-drt-access", "aws-shield-protections", "aws-sns-get-topic-attributes", "aws-sqs-get-queue-attributes", "aws-ssm-document", "aws-storage-gateway-cached-volume", "aws-storage-gateway-tape", "aws-support-case", "aws-transcribe-language-model", "aws-translate-terminology", "aws-trusted-advisor-check-result", "aws-vpc-transit-gateway", "aws-vpc-transit-gateway-attachment", "aws-waf-classic-regional-ip-set", "aws-waf-classic-web-acl-resource", "aws-waf-v2-global-ip-set", "aws-waf-v2-regional-ip-set", "aws-waf-v2-rule-group", "aws-waf-v2-web-acl-resource"], "source": ["fsi"], "app": ["xdr-st-*************-ca-collection-fsi-workers"]}, "warnings": ["String type is number. Bad practice because it may have too many different values. 'account_id=************'", "Cardinality of cloud_assets_collection_num_assets_processed_total is high: 332", "Average cardinality of cloud_assets_collection_num_assets_processed_total is high: 16766.0", "Number of targets for cloud_assets_collection_num_assets_processed_total is high: 4"], "hpa": {"ca-collection-fsi-workers": {"min": 1, "max": 100}}, "targets": [{"scrapeUrl": "http://***********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf6ps", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf22g", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_num_assets_processed_total Number of assets collected and submitted to the processing pipeline", "# TYPE cloud_assets_collection_num_assets_processed_total counter", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-access-analyzer\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-account-management-alternate-contact\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-acm-describe-certificate\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-api-gateway-authorizer\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-apigateway-domain-name\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-apigateway-method\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-apigateway-spec-export\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-apigatewayv2-api\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-app-stream-stack\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-appflow-flow\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-apprunner-auto-scaling-configuration\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-appsync-graphql-api\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-bedrock-agent-alias\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-bedrock-inference-profile\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-budgets-budget\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-chime-voice-connector\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cloudformation-describe-stacks\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cloudfront-response-headers-policy\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cloudtrail-describe-trails\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cloudtrail-get-event-selectors\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cloudwatch-insight-rule\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cloudwatch-log-group\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-code-artifact-domain\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-code-artifact-repository\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-code-build-project\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-code-commit-repository\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-code-pipeline-pipeline\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cognito-sync-pool-usage\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-comprehend-document-classifier\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-comprehendmedical-entities-detection-v2-jobs\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-configservice-aggregator\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-configservice-config-rules\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-datasync-agent\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-datasync-location\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-datasync-task\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-datasync-task-execution\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-dax-cluster\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-dax-parameter-group\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-describe-mount-targets\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-describe-workspace-directories\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-describe-workspaces\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-devops-guru-service-integration\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-dms-certificate\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-docdb-db-cluster\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-docdb-db-cluster-parameter-group\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ds-directory\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-autoscaling-launch-configuration\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-client-vpn-endpoint\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-describe-network-acls\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-describe-snapshots\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-describe-subnets\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-describe-vpn-connections\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-describe-vpn-gateways-summary\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-ebs-encryption\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-traffic-mirroring\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-vpc-endpoint-connection-notification\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-vpc-endpoint-service\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-vpc-ipam\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-vpc-stats\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-vpn-connections-summary\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ecr-get-repository-policy\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ecr-public-repositories\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ecs-cluster\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-efs-access-point\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-eks-fargate-profile\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-elasticache-cache-clusters\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-elasticache-describe-replication-groups\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-elasticache-reserved-cache-nodes\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-elasticache-user\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-elasticbeanstalk-configuration-settings\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-elasticbeanstalk-environment\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-elb-describe-load-balancers\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-elbv2-describe-load-balancers\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-elbv2-target-group\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-emr-security-configuration\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-es-describe-elasticsearch-domain\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-forecast-dataset\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-forecast-predictor\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-fsx-file-system\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-global-accelerator-accelerator\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-glue-connection\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-glue-data-brew-job\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-glue-schema\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-grafana-workspace\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-greengrass-core-definitions\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iam-get-account-password-policy\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iam-get-credential-report\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iam-identity-center-account-assignments\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iam-list-server-certificates\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iam-list-users\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-imagebuilder-image-pipeline\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-imagebuilder-infrastructure-configuration\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-inspector-v2-account-status\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-inspector-v2-permission\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iotfleetwise-signal-catalogs\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-kendra-index\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-kms-get-key-rotation-status\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-lex-bot\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-lexv2-bot\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-lookoutequipment-datasets\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-lookoutvision-projects\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-macie2-administrator-account\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-macie2-session\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-mediastore-container\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-mgn-launch-configuration-template\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-mq-broker\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-network-manager-core-network\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-networkfirewall-firewall\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-networkfirewall-firewall-policy\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-opensearch-serverless-collection\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-opsworks-user-profiles\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-pinpoint-email-channel\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-prometheus-workspace\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-quicksight-datasource\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-rds-option-group\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-redshift-describe-clusters\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-route53-health-check\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-s3-access-point\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-sagemaker-endpoint\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-sagemaker-notebook-instance\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-sagemaker-processing-job\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-sagemaker-studio-lifecycle-config\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-securityhub-standards\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-service-quota\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-servicecatalog-appregistry-applications\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-shield-advanced-status\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-shield-protection-groups\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-sns-platform-application\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ssm-inventory-instance-information\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ssm-parameter\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-storage-gateway-fileshare\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-storage-gateway-information\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-swf-domains\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-vpc-managed-prefix-list\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-vpc-transit-gateway-route-table\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-waf-classic-global-ip-set\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-waf-v2-global-web-acl-resource\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-well-architected-tool-workload\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-workspace-ip-group\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-xray-encryption-config\",source=\"fsi\"} 0"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-99jvp", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_num_assets_processed_total Number of assets collected and submitted to the processing pipeline", "# TYPE cloud_assets_collection_num_assets_processed_total counter", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-app-stream-stack\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-audit-manager-assessment\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cloud9-environment\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cognito-sync-pool-usage\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-comprehend-entities-detection-jobs\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-direct-connect-connection\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-dms-endpoint\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-dms-replication-task\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-launch-template\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ecs-cluster\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-fms-admin-account\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-grafana-workspace\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-greengrass-groups\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iot-analytics-channel\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-lambda-layer-version\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-migration-hub-home-region-control\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ram-resource-share\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-route53-domain\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-servicecatalog-appregistry-attribute-groups\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ses-identities\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ssm-parameter\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ssm-resource-compliance-summary\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-transfer-family-server\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-vpc-dhcp-options\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-workspace-bundle\",source=\"fsi\"} 0"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-klvwg", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_num_assets_processed_total Number of assets collected and submitted to the processing pipeline", "# TYPE cloud_assets_collection_num_assets_processed_total counter", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-account-contact-information\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-account-management-alternate-contact\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-acm-pca-certificate-authority\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-amplify-app\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-api-gateway-authorizer\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-apigateway-get-rest-apis\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-apigateway-get-stages\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-apigateway-method\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-apigateway-spec-export\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-apigatewayv2-api\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-app-stream-fleet\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-app-stream-stack\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-app-stream-usage-report-subscription\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-application-autoscaling-scaling-policy\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-appmesh-mesh\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-apprunner-auto-scaling-configuration\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-apprunner-service\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-appsync-graphql-api\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-audit-manager-assessment\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-audit-manager-control\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-backup-protected-resources\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-batch-job-definition\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-batch-job-queue\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-bedrock-agent-alias\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-bedrock-custom-model\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-bedrock-foundation-model\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-bedrock-inference-profile\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-bedrock-provisioned-model-throughput\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-budgets-budget\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cache-engine-versions\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-chime-voice-connector\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cloudformation-describe-stacks\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cloudfront-list-distributions\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cloudfront-origin-access-control\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cloudfront-response-headers-policy\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cloudhsm-cluster\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cloudsearch-domain\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cloudtrail-describe-trails\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cloudtrail-get-event-selectors\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cloudwatch-describe-alarms\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cloudwatch-insight-rule\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cloudwatch-log-group\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-code-artifact-domain\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-code-artifact-repository\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-code-build-source-credential\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-code-pipeline-pipeline\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-code-pipeline-webhook\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cognito-identity-pool\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-cognito-user-pool\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-comprehend-document-classifier\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-comprehend-document-classifier-summary\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-comprehend-entities-detection-jobs\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-configservice-aggregator\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-configservice-config-rules\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-configservice-describe-configuration-recorders\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-connect-instance\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-costexplorer-cost-and-usage\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-datapipeline-pipeline\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-datasync-agent\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-datasync-task\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-describe-account-attributes\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-describe-auto-scaling-groups\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-describe-mount-targets\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-describe-ssl-policies\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-describe-vpc-endpoints\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-detective-datasource-package\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-devops-guru-service-integration\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-direct-connect-gateway\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-dms-certificate\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-dms-replication-instance\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-dms-replication-task\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-docdb-db-cluster-parameter-group\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-drs-job\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-drs-source-server\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ds-directory\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-dynamodb-describe-table\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-customer-gateway\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-describe-flow-logs\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-describe-instances\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-describe-internet-gateways\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-describe-network-acls\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-describe-network-interfaces\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-describe-route-tables\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-describe-security-groups\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-describe-snapshots\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-describe-volumes\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-describe-vpc-peering-connections\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-describe-vpcs\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-describe-vpn-gateways\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-describe-vpn-gateways-summary\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-ebs-encryption\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-elastic-address\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-key-pair\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-launch-template\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-traffic-mirroring\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-vpc-endpoint-connection-notification\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-vpc-stats\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ec2-vpn-connections-summary\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ecr-get-repository-policy\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ecr-image\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ecr-public-image\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ecr-public-registry\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ecr-public-repositories\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ecr-registry\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ecr-registry-scanning-configuration\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ecs-cluster\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-efs-access-point\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-eks-describe-cluster\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-eks-node-group\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-elasticache-describe-replication-groups\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-elasticache-reserved-cache-nodes\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-elasticache-snapshots\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-elasticache-subnet-groups\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-elasticache-user\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-elasticbeanstalk-applications\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-elasticbeanstalk-configuration-settings\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-elasticbeanstalk-environment\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-elbv2-describe-load-balancers\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-elbv2-target-group\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-elbv2-target-health\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-emr-describe-cluster\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-emr-public-access-block\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-es-describe-elasticsearch-domain\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-events-eventbus\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-events-rule\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-fms-admin-account\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-forecast-dataset\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-forecast-predictor\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-fsx-backup\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-fsx-file-system\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-glacier-get-vault-lock\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-glacier-vault\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-global-accelerator-accelerator\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-glue-connection\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-glue-crawler\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-glue-datacatalog\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-glue-resource-policy\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-glue-schema\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-glue-security-configuration\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-grafana-workspace\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-greengrass-groups\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-guardduty-organization-configuration\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iam-get-account-summary\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iam-get-credential-report\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iam-get-policy-version\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iam-list-attached-user-policies\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iam-list-groups\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iam-list-roles\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iam-list-user-policies\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iam-list-users\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iam-list-virtual-mfa-devices\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iam-saml-provider\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iam-service-last-accessed-details\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-identity-store-group-members\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-identity-store-user\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-imagebuilder-component\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-imagebuilder-image-pipeline\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-imagebuilder-image-recipe\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-inspector-v2-account-status\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-inspector-v2-coverage\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-inspector-v2-filter\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-inspector-v2-permission\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iot-account-audit-configuration\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iot-analytics-channel\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iot-domain-configuration\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iot-events-inputs\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-iotfleetwise-signal-catalogs\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-kendra-index\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-kinesis-list-streams\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-kms-get-key-rotation-status\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-lake-formation-setting\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-lambda-code-signing-config\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-lambda-layer-version\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-lambda-list-functions\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-lexv2-bot\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-lightsail-instance\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-logs-describe-metric-filters\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-lookoutequipment-datasets\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-lookoutvision-projects\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-macie2-classification-job\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-macie2-session\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-managed-blockchain-networks\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-mediastore-container\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-memorydb-cluster\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-memorydb-parameter-group\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-mgn-launch-configuration-template\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-migration-hub-home-region-control\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-mq-broker\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-msk-cluster\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-mwaa-environment\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-neptune-db-cluster\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-network-firewall-rule-group\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-network-manager-global-network\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-networkfirewall-firewall\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-networkfirewall-firewall-policy\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-opensearch-serverless-collection\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-opsworks-user-profiles\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-organization\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-organizations-account\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-organizations-root\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-organizations-scp\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-organizations-tag-policy\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-polly-speech-synthesis-task\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-prometheus-workspace\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-qldb-ledger\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-quicksight-account-setting\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-quicksight-dataset\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-quicksight-datasource\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ram-principal\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-rds-db-cluster\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-rds-db-cluster-parameter-group\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-rds-db-cluster-snapshots\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-rds-describe-db-instances\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-rds-describe-db-parameter-groups\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-rds-describe-db-snapshots\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-rds-describe-event-subscriptions\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-rds-option-group\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-redshift-describe-clusters\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-region\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-resiliencehub-apps\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-route53-domain\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-route53-health-check\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-route53-list-hosted-zones\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-route53-query-logging-config\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-route53resolver-query-logging-config\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-route53resolver-query-logging-config-association\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-s3-access-point\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-s3-batch-operation\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-s3api-get-bucket-acl\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-s3control-public-access-block\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-sagemaker-app\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-sagemaker-code-repository\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-sagemaker-domain\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-sagemaker-endpoint-config\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-sagemaker-hyper-parameter-tuning-job\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-sagemaker-model\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-sagemaker-notebook-instance-lifecycle-config\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-sagemaker-processing-job\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-sagemaker-studio-lifecycle-config\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-sagemaker-training-job\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-sagemaker-transform-job\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-sagemaker-user-profile\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-secretsmanager-describe-secret\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-securityhub-enabled-standards\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-securityhub-hub\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-securityhub-standards\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-serverlessrepo-application\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-servicecatalog-appregistry-attribute-groups\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-servicecatalog-portfolios\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-servicediscovery-namespaces\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-shield-advanced-status\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-shield-drt-access\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-shield-protection-groups\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-shield-protections\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-sns-get-topic-attributes\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-sns-platform-application\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-sqs-get-queue-attributes\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ssm-document\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ssm-inventory-instance-information\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ssm-parameter\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-ssm-resource-compliance-summary\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-storage-gateway-cached-volume\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-storage-gateway-fileshare\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-storage-gateway-tape\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-support-case\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-swf-domains\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-transcribe-language-model\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-transfer-family-server\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-translate-terminology\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-trusted-advisor-check-result\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-vpc-dhcp-options\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-vpc-managed-prefix-list\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-vpc-transit-gateway\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-vpc-transit-gateway-attachment\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-vpc-transit-gateway-route-table\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-waf-classic-global-ip-set\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-waf-classic-regional-ip-set\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-waf-classic-web-acl-resource\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-waf-v2-global-ip-set\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-waf-v2-global-web-acl-resource\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-waf-v2-regional-ip-set\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-waf-v2-rule-group\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-waf-v2-web-acl-resource\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-workspace-ip-group\",source=\"fsi\"} 0", "cloud_assets_collection_num_assets_processed_total{account_id=\"************\",asset_type=\"aws-xray-encryption-config\",source=\"fsi\"} 0"]}], "cardinality_formula": {"per_pod": "1(account_id labels) * 332(asset_type labels) * 1(source labels) * 1(app labels)", "current": "1(account_id labels) * 332(asset_type labels) * 1(source labels) * 1(app labels) * 4(targets)", "min": "1(account_id labels) * 332(asset_type labels) * 1(source labels) * 1(app labels) * (1(ca-collection-fsi-workers pods))", "max": "1(account_id labels) * 332(asset_type labels) * 1(source labels) * 1(app labels) * (100(ca-collection-fsi-workers pods))", "avg": "((1(account_id labels) * 332(asset_type labels) * 1(source labels) * 1(app labels) * (1(ca-collection-fsi-workers pods))) + (1(account_id labels) * 332(asset_type labels) * 1(source labels) * 1(app labels) * (100(ca-collection-fsi-workers pods)))) / 2"}, "cardinality": {"per_pod": 332, "current": 1328, "min": 332, "max": 33200, "avg": 16766.0}}, {"name": "cloud_assets_collection_num_rit_failures_total", "labels": {"message": ["failed to retrieve credentials: get credentials failed, accoundID: ************, status: 403"], "step_type": ["map", "iterate"], "app": ["xdr-st-*************-ca-collection-fsi-workers"]}, "warnings": ["There is space symbol in value. Please check the value. 'message=failed to retrieve credentials: get credentials failed, accoundID: ************, status: 403'", "Number of targets for cloud_assets_collection_num_rit_failures_total is high: 4"], "hpa": {"ca-collection-fsi-workers": {"min": 1, "max": 100}}, "targets": [{"scrapeUrl": "http://***********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf6ps", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-bf22g", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": []}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-99jvp", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_num_rit_failures_total Count the number of rits that has failed due to non API steps. Recording the step type that has failed as well", "# TYPE cloud_assets_collection_num_rit_failures_total counter", "cloud_assets_collection_num_rit_failures_total{message=\"failed to retrieve credentials: get credentials failed, accoundID: ************, status: 403\",step_type=\"map\"} 33"]}, {"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-fsi-workers-dfc6ccdf-klvwg", "app": "xdr-st-*************-ca-collection-fsi-workers", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "ca-collection-fsi-workers"}], "raw_metric": ["# HELP cloud_assets_collection_num_rit_failures_total Count the number of rits that has failed due to non API steps. Recording the step type that has failed as well", "# TYPE cloud_assets_collection_num_rit_failures_total counter", "cloud_assets_collection_num_rit_failures_total{message=\"failed to retrieve credentials: get credentials failed, accoundID: ************, status: 403\",step_type=\"iterate\"} 198", "cloud_assets_collection_num_rit_failures_total{message=\"failed to retrieve credentials: get credentials failed, accoundID: ************, status: 403\",step_type=\"map\"} 100"]}], "cardinality_formula": {"per_pod": "1(message labels) * 2(step_type labels) * 1(app labels)", "current": "1(message labels) * 2(step_type labels) * 1(app labels) * 4(targets)", "min": "1(message labels) * 2(step_type labels) * 1(app labels) * (1(ca-collection-fsi-workers pods))", "max": "1(message labels) * 2(step_type labels) * 1(app labels) * (100(ca-collection-fsi-workers pods))", "avg": "((1(message labels) * 2(step_type labels) * 1(app labels) * (1(ca-collection-fsi-workers pods))) + (1(message labels) * 2(step_type labels) * 1(app labels) * (100(ca-collection-fsi-workers pods)))) / 2"}, "cardinality": {"per_pod": 2, "current": 8, "min": 2, "max": 200, "avg": 101.0}}, {"name": "cloud_assets_collection_num_handle_account_failures_total", "labels": {"source": ["coordinator"], "app": ["xdr-st-*************-ca-collection-coordinator"]}, "warnings": [], "hpa": {"ca-collection-coordinator": {"min": 1, "max": 5}}, "targets": [{"scrapeUrl": "http://**********:8899/metrics", "pod_name": "xdr-st-*************-ca-collection-coordinator-8cd98745-gv4nv", "app": "xdr-st-*************-ca-collection-coordinator", "image": [{"image": "us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway:collection-CRTX-185416_emitting_metrics_ca_collection-v4.3.0-1265-g2d03c", "container": "xdr-st-*************-ca-collection-coordinator"}], "raw_metric": ["# HELP cloud_assets_collection_num_handle_account_failures_total Count the number of failures encountered from handle account function which creates cloud resources (rit) tasks for each cloud account", "# TYPE cloud_assets_collection_num_handle_account_failures_total counter", "cloud_assets_collection_num_handle_account_failures_total{source=\"coordinator\"} 4"]}], "cardinality_formula": {"per_pod": "1(source labels) * 1(app labels)", "current": "1(source labels) * 1(app labels) * 1(targets)", "min": "1(source labels) * 1(app labels) * (1(ca-collection-coordinator pods))", "max": "1(source labels) * 1(app labels) * (5(ca-collection-coordinator pods))", "avg": "((1(source labels) * 1(app labels) * (1(ca-collection-coordinator pods))) + (1(source labels) * 1(app labels) * (5(ca-collection-coordinator pods)))) / 2"}, "cardinality": {"per_pod": 1, "current": 1, "min": 1, "max": 5, "avg": 3.0}}]