name: Formatting validation using black

on:
  push:
    branches: [main]
  pull_request:

jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    strategy:
      matrix:
        python-version: ["3.13"]

    steps:
      - uses: actions/checkout@v5
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        run: |
          pip install -U pip
          pip install -r requirements.txt
      - name: Format with black
        run: |
          black .
          if git status --porcelain | grep .; then git --no-pager diff; exit 1; fi
